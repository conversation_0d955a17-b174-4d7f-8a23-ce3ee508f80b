# Understand.me - Development Rules & Documentation Map

## 1. Introduction

**Purpose of this File:**

This `development.rules` file serves two primary purposes for the "Understand.me" development team:

1.  **Documentation Map:** To provide a quick reference and map to the core documentation artifacts created for the project, ensuring developers can easily find the information they need.
2.  **Development Rules & Conventions:** To establish a set of essential rules, conventions, and best practices for software development, specifically tailored to our technology stack (Expo, React Native, TypeScript, Supabase, AI Orchestration Layer, and related services). Adherence to these rules is critical for maintaining code quality, consistency, and collaboration efficiency.

All developers working on "Understand.me" are expected to familiarize themselves with and adhere to the guidelines outlined in this document and the referenced guides.

## 2. Documentation File Map

The following are the core documentation artifacts for the "Understand.me" project:

1.  **`understand_me_mermaid_flow updated.mermaid`:**
    *   **Location:** Root directory.
    *   **Content:** The master Mermaid diagram detailing all user flows, screen interactions, and decision points across the entire application. It serves as the visual blueprint for application behavior.
2.  **`docs/development_guide/README.md` (UI Development Guide):**
    *   **Location:** `docs/development_guide/`
    *   **Content:** A multi-part guide (Parts 1-10) focused on the **User Interface and User Experience (UI/UX)** of the Expo (React Native) mobile application. It details global design principles, onboarding flows, dashboard navigation, user paths for hosts and participants, the AI-mediated session interface (five phases), post-session activities, the growth module, and shared UI components/patterns from a UI/UX perspective. It defines how the application should look, feel, and behave for the end-user, including detailed descriptions of Udine's (AI Agent) persona and interactions.
3.  **`docs/developer_guide/README.md` (Developer's Guide):**
    *   **Location:** `docs/developer_guide/`
    *   **Content:** A multi-part guide (Parts 1-9) aimed at **human software engineers** building the application. It covers system overview, development environment setup, core backend data management (Supabase), AI orchestration (AI Orchestration Layer & Google GenAI), detailed external service integrations, feature implementation guides (linking UI to backend logic), Expo/React Native best practices, testing strategies, and deployment/operations.
4.  **`docs/technical_product_knowledge_base/README.md` (TPKB):**
    *   **Location:** `docs/technical_product_knowledge_base/`
    *   **Content:** A multi-part knowledge base (Parts 1-9) designed to be a **deep technical reference** synthesizing all project information. It focuses on the intricate details of technical requirements derived from product vision, comprehensive feature breakdowns mapping to services, deep dives into service integrations, data flow architecture, serverless design patterns, SPR considerations, security architecture, a decision log, and a glossary.
5.  **`docs/combined_system_spec.md` (Combined System Specification & PRD):**
    *   **Location:** `docs/`
    *   **Content:** Consolidated system specification and product requirements document including agent overview, product requirements, user personas, system-wide functional requirements, system architecture, integration details, and data flow diagrams.
6.  **`docs/development.rules` (This File):**
    *   **Location:** `docs/`
    *   **Content:** This document, providing a map to other documentation and defining key programming rules, conventions, and critical reminders for development.

## 3. Programming Rules & Conventions (Expo / React Native / TypeScript)

### 3.1. General Principles
*   **Readability & Maintainability:** Write clear, concise, and self-documenting code where possible. Prioritize long-term maintainability.
*   **DRY (Don't Repeat Yourself):** Avoid code duplication by creating reusable components, functions, and hooks.
*   **KISS (Keep It Simple, Stupid):** Favor simpler solutions over overly complex ones, unless complexity is justified by requirements.
*   **SOLID Principles:** Apply SOLID principles where applicable in object-oriented or component design.
*   **Follow the Guides:** Adhere to the architectural patterns, UI guidelines, and implementation details specified in the UI Development Guide, Developer's Guide, and TPKB.

### 3.2. Naming Conventions
*   **Components (React Native):** PascalCase (e.g., `PrimaryButton.tsx`, `UserProfileCard.tsx`).
*   **Files:**
    *   Components: PascalCase (e.g., `SessionItem.tsx`).
    *   Services, Hooks, Utils, Stores: camelCase (e.g., `authService.ts`, `useSessionData.ts`, `formattingUtils.ts`, `userStore.ts`).
    *   Test files: `*.test.ts` or `*.spec.ts` (e.g., `PrimaryButton.test.tsx`).
*   **Functions/Methods:** camelCase (e.g., `handleLogin`, `fetchUserProfile`).
*   **Variables:** camelCase (e.g., `let userName;`). For boolean variables, consider prefixes like `is`, `has`, `should` (e.g., `isLoading`, `hasPermission`).
*   **Constants:** SCREAMING_SNAKE_CASE (e.g., `MAX_PARTICIPANTS`, `DEFAULT_AVATAR_URL`). Store in relevant files within `constants/`.
*   **Interfaces/Types (TypeScript):** PascalCase (e.g., `interface UserProfile`, `type SessionStatus`).

### 3.3. Component Structure (React Native)
*   **Functional Components:** Use functional components with React Hooks.
*   **Props:** Define props using TypeScript interfaces or types. Keep props minimal and focused.
*   **Separation of Concerns:** Separate presentational logic (JSX, styles) from business logic (state updates, API calls - often moved to custom hooks or services).
*   **Directory Structure:** Group components by feature or commonality as per Dev Guide 7.1.

### 3.4. State Management (Zustand)
*   **Primary Choice:** Zustand is the primary state management library (Dev Guide 7.3).
*   **Store Granularity:** Create separate stores (slices) for different logical domains (e.g., `userStore`, `sessionStore`).
*   **Selectors:** Use selectors to access specific pieces of state to optimize re-renders.
*   **Async Actions:** Embed asynchronous logic (API calls) within store actions.
*   **Immutability:** Treat state as immutable when updating.

### 3.5. Styling (StyleSheet API / NativeWind Option)
*   **Primary Choice:** Use React Native's `StyleSheet.create` API for styling (Dev Guide 7.4).
*   **Theming:** Utilize a central theme file (`constants/theme.ts`) for colors, spacing, typography.
*   **NativeWind:** If the project or a specific complex component benefits significantly from utility-first classes and the team agrees, NativeWind can be used, but `StyleSheet` is the default for consistency.
*   **Platform-Specific Styles:** Use `Platform.select()` for minor OS-specific adjustments.

### 3.6. TypeScript Usage
*   **Strong Typing:** Leverage TypeScript's static typing to catch errors early. Define types/interfaces for props, state, API payloads, and function signatures.
*   **Avoid `any`:** Minimize the use of `any`. If unavoidable, provide a clear reason in comments.
*   **Strict Mode:** Enable strict mode options in `tsconfig.json` (`strict: true`, `noImplicitAny: true`, etc.).
*   **Generated Types:** Use auto-generated types from Supabase (`supabase gen types typescript`).

### 3.7. Linting & Formatting
*   **ESLint & Prettier:** The project must be configured with ESLint for code quality and Prettier for consistent code formatting.
*   **Configuration:** Adhere to the shared ESLint and Prettier configurations defined in the project.
*   **Pre-commit Hooks:** Use pre-commit hooks (e.g., with Husky and lint-staged) to automatically lint and format code before committing.

### 3.8. API Service Calls
*   **Centralized Services:** Encapsulate API interactions within dedicated service modules (e.g., `aiOrchestrationApiService.ts`, `supabaseService.ts` in `services/`).
*   **Error Handling:** Implement robust error handling for all API calls (network errors, API-specific errors).
*   **Loading States:** Manage and display appropriate loading states in the UI when data is being fetched or submitted.
*   **Async/Await:** Use `async/await` for managing asynchronous operations.

### 3.9. Accessibility (React Native)
*   Adhere to guidelines in UI Dev Guide 1.6 and Dev Guide 7.7.
*   Use `accessibilityLabel`, `accessibilityHint`, `accessibilityRole`, `accessibilityState` props correctly.
*   Ensure sufficient touch target sizes.
*   Test with VoiceOver (iOS) and TalkBack (Android).
*   Support dynamic type/font scaling.

### 3.10. Comments & Code Documentation
*   **Clarity Over Comments:** Write self-documenting code where possible.
*   **Purposeful Comments:** Add comments to explain complex logic, non-obvious decisions, or workarounds.
*   **JSDoc/TSDoc:** Use JSDoc/TSDoc syntax for documenting functions, components, props, and types, especially for shared or complex pieces of code.
*   **TODOs:** Use `// TODO:` comments for pending work, ideally with a reference to a ticket or issue.

### 3.11. Continuous Integration (CI)
*   **CI Provider:** GitHub Actions.
*   **Main Workflow Steps:** `install → lint → test → build → deploy-preview`.
*   **Caching:** Use `actions/setup-node` with yarn/npm cache to speed builds.
*   **Secrets Management:** API keys/secrets are stored in GitHub Secrets and injected at runtime.

### 3.12. Testing
*   Follow testing strategies outlined in Developer's Guide Part 8.
*   **Unit Tests:** For components (React Native Testing Library), hooks, and utilities (Jest). Aim for good coverage of critical logic.
*   **Integration Tests:** For interactions between components and services (mocking external dependencies).
*   **E2E Tests:** For critical user flows (using Detox, Appium, or similar).
*   Write tests alongside development, not just as an afterthought.

### 3.12. Git Workflow
*   **Branching Strategy (GitHub Flow):**
    *   `main` is always deployable.
    *   Create short-lived `feature/<topic>` branches off `main`.
    *   Open a Draft Pull Request early to enable CI and feedback.
    *   Rebase onto `main` frequently; squash-merge via PR when approved.
    *   Hotfixes are performed directly from `main` followed by a patch release tag.
*   **Release Tags:** Semantic version tags (`vMAJOR.MINOR.PATCH`) are created on `main` after each production deployment (handled by GitHub Actions workflow).
*   **Commit Messages:** Follow conventional commit message format (e.g., `feat: add user login screen`, `fix: resolve issue with session timer`).
*   **Pull Requests (PRs):**
    *   All code changes must go through PRs.
    *   PRs should be reviewed by at least one other developer.
    *   Ensure CI checks (linting, tests, build) pass before merging.
    *   Keep PRs focused and reasonably sized.
*   **Code Reviews:** Provide constructive, respectful feedback. Focus on adherence to guidelines, correctness, and maintainability.

## 4. Critical Notes & Reminders for Development

*   **Supabase as BaaS:** Supabase is the source of truth for core data (profiles, sessions, messages, etc.) and authentication. Utilize its features (PostgreSQL, Auth, Storage, Realtime, Edge Functions) effectively. RLS is key for security.
*   **Service Responsibilities (High-Level):**
    *   **Dappier:** Real-time data streams, specialized RAG data sources.
    *   **Nodely:** IPFS integration for specific artifacts, potentially complex workflow execution.
    *   **Upstash Redis:** Caching layer for AI Orchestration Layer and Supabase Edge Functions to improve performance and reduce costs.
    *   **ElevenLabs:** TTS for Udine's voice.
    *   **Google GenAI:** Core LLM tasks (analysis, summarization, script generation) and STT/Vision.
    *   **LangChain JS:** Agent/executor framework used within AI Orchestration Layer for tool routing, memory, and agentic workflows.
*   **Udine's Persona:** All AI interactions and UI representations of Udine must align with its defined persona (UI Dev Guide 1.4, 7.1.B, 10.2).
*   **Mermaid Diagram Importance:** The `understand_me_mermaid_flow updated.mermaid` is the primary reference for user flows and application structure. Ensure feature development aligns with it.
*   **Documentation Driven:** Refer to the UI Development Guide, Developer's Guide, and TPKB *before* and *during* development. Update documentation if changes are made.
*   **Local Development Tools (User Choice & Project Standards):**
    *   While Supabase CLI and Docker are recommended for a full local Supabase environment, developers can also use a shared cloud dev instance of Supabase if preferred and project policy allows.
    *   Choice of IDE (VS Code recommended) is up to the developer, but ensure adherence to project linting/formatting.
    *   Use Expo Go on physical devices or platform emulators/simulators for testing.
*   **Security & Privacy:** Implement security and privacy by design. Adhere to guidelines in TPKB Part 7 and Dev Guide Part 3 (RLS).
*   **Performance:** Keep mobile performance in mind (Dev Guide 7.6).
