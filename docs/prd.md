# Understand.me - Product Requirements Document (PRD)

## 1. Executive Summary

### 1.1. Product Vision
"Understand.me" is an AI-mediated communication platform that transforms difficult conversations into opportunities for deeper understanding and connection. The platform features "Udine," an empathetic AI voice agent that guides participants through a structured 5-phase mediation process using advanced emotional intelligence, natural turn-taking conversation, and real-time conflict analysis.

### 1.2. Unified Architecture (2024)
**Development Environment**: bolt.new optimized for rapid iteration
**Deployment**: Netlify with Express.js backend (non-serverless)
**Frontend**: Expo (React Native) - Universal app for Mobile, Web, and Desktop
**Backend**: Express.js/Node.js with PostgreSQL database
**State Management**: Zustand for lightweight, efficient state handling
**AI Orchestration**: Lang<PERSON>hain JS + LangGraph for intelligent workflow management
**Voice Agent**: Udine (ElevenLabs turn-taking AI with emotional adaptation)
**Emotional Intelligence**: Hume AI for real-time emotion detection and analysis

### 1.3. Core AI Stack
- **Google GenAI 1.5.0**: Primary LLM for conversation analysis, conflict understanding, and response generation
- **LangChain JS Community**: AI orchestration with specialized plugins for mediation workflows
- **LangGraph**: Agent-based workflow management for the 5-phase mediation process
- **Hume AI**: Real-time emotional intelligence analysis from voice, text, and behavioral patterns
- **ElevenLabs**: Turn-taking conversational AI with Udine's empathetic voice personality

### 1.4. Revolutionary Features
- **5-Phase Structured Mediation**: Preparation → Exploration → Understanding → Resolution → Healing
- **Emotional Intelligence Integration**: Real-time emotion detection with adaptive AI responses
- **Turn-Taking Voice Conversations**: Natural, interruption-free dialogue with Udine
- **Cross-Platform Universality**: Single codebase for mobile, web, and desktop experiences
- **Conflict Pattern Recognition**: AI-powered analysis of communication breakdowns and resolution pathways
- **Personal Growth Tracking**: Emotional intelligence development and communication skill building

## 2. Technical Architecture

### 2.1. Unified System Architecture
```
┌─────────────────────────────────────────────────────────────────────┐
│                        Client Layer (Expo Universal)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────┐  │
│  │   Mobile App    │  │    Web App      │  │   Desktop App       │  │
│  │   (iOS/Android) │  │   (React DOM)   │  │   (Electron)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────────┘  │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐    │
│  │              Shared Components & State (Zustand)            │    │
│  │  • UdineVoiceAgent  • EmotionalInsights  • SessionPhases   │    │
│  └─────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────┐
│                     Express.js Backend (Netlify)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────┐  │
│  │   API Routes    │  │   Auth Service  │  │   Session Manager   │  │
│  │   /api/auth     │  │   JWT + bcrypt  │  │   5-Phase Workflow  │  │
│  │   /api/sessions │  │                 │  │                     │  │
│  │   /api/emotions │  │                 │  │                     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────┐
│                         AI Services Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────┐  │
│  │   LangChain JS  │  │   Google GenAI  │  │      Hume AI        │  │
│  │  + LangGraph    │  │     1.5.0       │  │  (Emotional Intel)  │  │
│  │  (Orchestration)│  │   (Primary LLM) │  │                     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐                          │
│  │   ElevenLabs    │  │   PostgreSQL    │                          │
│  │  (Udine Voice)  │  │   (Database)    │                          │
│  └─────────────────┘  └─────────────────┘                          │
└─────────────────────────────────────────────────────────────────────┘
```

### 2.2. Udine Voice Agent Integration (ElevenLabs)
Following the [Expo + ElevenLabs Conversational AI guide](https://expo.dev/blog/how-to-build-universal-app-voice-agents-with-expo-and-elevenlabs), our implementation provides:

**Turn-Taking Conversation Flow:**
- Natural conversation rhythm with automatic turn detection
- Real-time voice processing with <2s latency
- Seamless integration with Expo's audio capabilities across all platforms
- Interruption handling and conversation state management

**Udine's Personality & Capabilities:**
- Consistent, empathetic voice personality across all interactions
- Emotional tone adaptation based on real-time Hume AI analysis
- Context-aware responses powered by LangChain JS + Google GenAI
- Phase-specific guidance for the 5-stage mediation process
- Conflict de-escalation and emotional regulation techniques

### 2.3. Intelligent Data Flow Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Input    │───▶│   ElevenLabs    │───▶│   LangChain     │
│ (Voice/Text)    │    │  (Turn-Taking)  │    │  (Orchestration)│
└─────────────────┘    └─────────────────┘    └─────────┬───────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────▼───────┐
│   Hume AI       │◀───│  Express.js API │◀───│  Google GenAI   │
│ (Emotion Detect)│    │  (Coordination) │    │  (Analysis)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────────────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │   Zustand       │
│ (Persistence)   │    │ (Client State)  │
└─────────────────┘    └─────────────────┘
```

**Key Data Flows:**
1. **Input Processing**: Voice/text → ElevenLabs → LangChain orchestration
2. **Emotional Analysis**: Parallel processing through Hume AI for real-time insights
3. **AI Response Generation**: Google GenAI creates contextually appropriate responses
4. **State Synchronization**: PostgreSQL persistence + Zustand client state management
5. **Voice Synthesis**: Emotionally-adapted voice output through ElevenLabs

## 3. Core Features & User Stories

### 3.1. User Authentication & Profiles
**As a user, I want to:**
- **Quick Registration**: Sign up with email/password with immediate access to Udine
- **Personality Assessment**: Complete an AI-guided assessment to personalize my experience
- **Privacy Control**: Manage data sharing preferences and conversation recording settings
- **Growth Tracking**: Access my emotional intelligence development and communication insights
- **Session History**: Review past conversations, summaries, and personal growth metrics

### 3.2. Udine AI Voice Agent
**As a user, I want to:**
- **Natural Conversations**: Engage in turn-taking dialogue that feels genuinely human
- **Emotional Responsiveness**: Receive responses that adapt to my emotional state in real-time
- **Conflict Guidance**: Get expert mediation guidance during difficult conversations
- **Empathetic Understanding**: Feel truly heard and validated by an AI that understands context
- **Phase Navigation**: Be guided through the 5-phase mediation process with clear direction
- **De-escalation Support**: Receive immediate intervention when tensions rise

### 3.3. Session Management
**As a host, I want to:**
- **Easy Session Creation**: Set up mediation sessions with conflict type selection and context
- **Flexible Invitations**: Invite participants via email, SMS, or shareable links
- **Session Configuration**: Control privacy, recording, anonymity, and participation settings
- **Real-time Monitoring**: Track participant engagement and emotional states during sessions
- **Progress Oversight**: Monitor the 5-phase progression and intervention opportunities

**As a participant, I want to:**
- **Simple Joining**: Enter sessions with one-click links or short codes
- **Context Understanding**: Review session background and objectives before participating
- **Privacy Assurance**: Control my data sharing and recording preferences
- **Safe Environment**: Feel protected by AI moderation and conflict prevention measures
- **Meaningful Participation**: Contribute effectively with Udine's guidance and support

### 3.4. Revolutionary 5-Phase Mediation Workflow
**Phase 1: Preparation** 🎯
- **Emotional Baseline**: Hume AI establishes each participant's emotional starting point
- **Context Setting**: Udine guides participants through conflict articulation and goal setting
- **Trust Building**: Establishing psychological safety and communication ground rules
- **Expectation Alignment**: Clear understanding of the process and desired outcomes

**Phase 2: Exploration** 🔍
- **Perspective Sharing**: Structured storytelling with Udine's active listening facilitation
- **Emotional Monitoring**: Real-time emotion tracking with adaptive response modulation
- **Pattern Recognition**: LangChain analysis identifies communication patterns and triggers
- **Clarification Support**: Udine asks probing questions to deepen understanding

**Phase 3: Understanding** 💡
- **Empathy Building**: Guided perspective-taking exercises with AI coaching
- **Need Identification**: Uncovering underlying needs, fears, and motivations
- **Common Ground Discovery**: Highlighting shared values and mutual interests
- **Emotional Validation**: Ensuring all participants feel heard and understood

**Phase 4: Resolution** 🤝
- **Solution Co-creation**: Collaborative brainstorming with AI-suggested options
- **Feasibility Assessment**: Evaluating proposed solutions for practicality and fairness
- **Agreement Drafting**: Structured creation of mutually acceptable resolutions
- **Commitment Securing**: Ensuring genuine buy-in from all participants

**Phase 5: Healing** 🌱
- **Relationship Repair**: Addressing emotional wounds and rebuilding trust
- **Future Planning**: Creating sustainable communication strategies
- **Accountability Framework**: Establishing check-ins and progress monitoring
- **Growth Integration**: Connecting insights to personal development goals

### 3.5. Advanced Emotional Intelligence Integration
**Hume AI Capabilities:**
- **Multi-modal Emotion Detection**: Voice tone, speech patterns, and text sentiment analysis
- **Real-time Emotional Tracking**: Continuous monitoring throughout conversations
- **Predictive Intervention**: Early warning system for emotional escalation
- **Personalized Insights**: Individual emotional patterns and growth recommendations
- **Cultural Sensitivity**: Emotion recognition adapted for diverse cultural expressions
- **Trauma-Informed Responses**: Specialized handling of trauma-related emotional states

## 4. Technical Implementation

### 4.1. Frontend Development (Expo Universal)
**Complete Technology Stack:**
```typescript
// Core Expo & React Native
"expo": "~51.0.0",
"react-native": "0.74.0",
"react-native-web": "~0.19.10",

// AI & Voice Integration
"@elevenlabs/react": "^0.8.0",        // Turn-taking conversation SDK
"@elevenlabs/client": "^0.8.0",       // ElevenLabs core client
"@google/genai": "^1.5.0",            // Google GenAI 1.5.0 (latest)
"@langchain/core": "^0.3.0",          // LangChain abstractions
"@langchain/community": "^0.3.0",     // Community plugins for RAG/memory
"@langchain/google-genai": "^0.1.0",  // Google GenAI integration
"@langchain/langgraph": "^0.2.0",     // Agent workflow orchestration
"@langchain/textsplitters": "^0.1.0", // Document processing
"hume": "^0.9.0",                     // Hume AI emotional intelligence

// State & Data Management
"zustand": "^4.5.5",                  // Lightweight state management
"zod": "^3.23.8",                     // Runtime type validation
"@react-native-async-storage/async-storage": "^1.23.1",

// Audio & Media
"expo-av": "~14.0.6",                 // Audio recording/playback
"expo-constants": "~16.0.2",          // Environment configuration
"expo-status-bar": "~1.12.1",         // Status bar management

// Utilities
"uuid": "^10.0.0",                    // Unique ID generation
"cheerio": "^1.0.0"                   // HTML parsing for documents
```

**Key Frontend Components:**
- **UdineVoiceAgent**: ElevenLabs turn-taking conversation interface
- **EmotionalInsights**: Real-time Hume AI emotion visualization
- **SessionPhases**: 5-phase workflow progress and navigation
- **DashboardScreen**: Session management and personal insights
- **SessionScreen**: Active mediation interface with multi-tab layout

### 4.2. Backend Development (Express.js/Node.js)
**Complete Server Architecture:**
```javascript
// Core Express.js setup with unified architecture
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
require('dotenv').config();

// AI Service Integrations
const { GoogleGenerativeAI } = require('@google/genai');
const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
const { createReactAgent } = require('@langchain/langgraph/prebuilt');
const { MemorySaver } = require('@langchain/langgraph');
const { HumeClient } = require('hume');

// Express App Configuration
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API Route Structure
app.use('/api/auth', require('./routes/auth'));           // Authentication & user management
app.use('/api/sessions', require('./routes/sessions'));   // Session creation & management
app.use('/api/conversations', require('./routes/conversations')); // Messages & Udine responses
app.use('/api/emotions', require('./routes/emotions'));   // Hume AI emotional analysis

// Health & Status
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    services: {
      ai: 'Google GenAI 1.5.0',
      orchestration: 'LangChain JS',
      emotions: 'Hume AI',
      voice: 'ElevenLabs (Udine)'
    }
  });
});
```

**Complete Database Schema (PostgreSQL):**
```sql
-- Users and authentication
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Mediation sessions with 5-phase tracking
CREATE TABLE sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  conflict_type VARCHAR(100) DEFAULT 'general',
  status VARCHAR(50) DEFAULT 'created',
  current_phase VARCHAR(50) DEFAULT 'preparation',
  phases JSONB DEFAULT '{}',
  participants JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Conversation messages with AI analysis
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  speaker VARCHAR(100) NOT NULL,
  message_type VARCHAR(50) DEFAULT 'user',
  emotional_analysis JSONB,
  udine_response JSONB,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Emotional insights from Hume AI
CREATE TABLE emotional_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  emotions JSONB NOT NULL,
  sentiment JSONB NOT NULL,
  voice_analysis JSONB,
  recommendations JSONB,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Session participants with roles
CREATE TABLE session_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  role VARCHAR(50) DEFAULT 'participant',
  joined_at TIMESTAMP DEFAULT NOW()
);

-- LangChain conflict analysis results
CREATE TABLE conflict_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
  analysis_type VARCHAR(100) NOT NULL,
  analysis_data JSONB NOT NULL,
  recommendations JSONB,
  confidence_score DECIMAL(3,2),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 4.3. Advanced AI Service Integration
**LangChain JS + LangGraph Orchestration:**
```javascript
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumeClient } from "hume";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { MemorySaver } from "@langchain/langgraph";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";

class UdineOrchestrator {
  constructor() {
    // Initialize Google GenAI 1.5.0
    this.llm = new ChatGoogleGenerativeAI({
      modelName: "gemini-1.5-pro",
      apiKey: process.env.GOOGLE_GENAI_API_KEY,
      temperature: 0.7,
      maxTokens: 4000
    });

    // Initialize Hume AI for emotional intelligence
    this.hume = new HumeClient({
      apiKey: process.env.HUME_API_KEY,
      secretKey: process.env.HUME_SECRET_KEY
    });

    // Initialize LangGraph agent with memory
    this.memory = new MemorySaver();
    this.agent = createReactAgent({
      llm: this.llm,
      tools: [new TavilySearchResults({ maxResults: 3 })],
      checkpointSaver: this.memory
    });
  }

  async processConversationTurn(userInput, sessionContext, currentPhase) {
    // 1. Parallel processing: Emotion analysis + Content analysis
    const [emotionalAnalysis, contentAnalysis] = await Promise.all([
      this.analyzeEmotions(userInput, sessionContext),
      this.analyzeContent(userInput, sessionContext, currentPhase)
    ]);

    // 2. Generate phase-appropriate response using LangGraph
    const agentResponse = await this.agent.invoke(
      {
        messages: [
          {
            role: "system",
            content: this.buildPhaseSystemPrompt(currentPhase, emotionalAnalysis, sessionContext)
          },
          {
            role: "user",
            content: userInput
          }
        ]
      },
      { configurable: { thread_id: sessionContext.sessionId } }
    );

    // 3. Adapt voice characteristics based on emotional context
    const voiceSettings = this.adaptUdineVoice(emotionalAnalysis, currentPhase);

    // 4. Return comprehensive response for ElevenLabs synthesis
    return {
      text: agentResponse.messages[agentResponse.messages.length - 1].content,
      emotionalAnalysis,
      contentAnalysis,
      voiceSettings,
      phaseGuidance: this.getPhaseGuidance(currentPhase, contentAnalysis),
      interventionNeeded: this.assessInterventionNeed(emotionalAnalysis)
    };
  }

  async analyzeEmotions(input, context) {
    // Multi-modal emotion analysis with Hume AI
    const analysis = await this.hume.expressionMeasurement.batch.startInferenceJob({
      text: [input],
      models: {
        language: {
          granularity: "sentence",
          identify_speakers: true
        }
      }
    });

    return {
      primary: analysis.results[0]?.predictions?.language?.emotions?.[0] || 'neutral',
      intensity: analysis.results[0]?.predictions?.language?.emotions?.[0]?.score || 0.5,
      sentiment: analysis.results[0]?.predictions?.language?.sentiment || 'neutral',
      confidence: analysis.results[0]?.predictions?.language?.confidence || 0.8
    };
  }

  buildPhaseSystemPrompt(phase, emotions, context) {
    const phasePrompts = {
      preparation: `You are Udine, an empathetic AI mediator in the PREPARATION phase.
        Focus on: establishing trust, understanding context, setting expectations.
        Current emotional state: ${emotions.primary} (intensity: ${emotions.intensity})
        Adapt your tone to be ${emotions.intensity > 0.7 ? 'calming and supportive' : 'warm and encouraging'}.`,

      exploration: `You are Udine, facilitating the EXPLORATION phase.
        Focus on: encouraging perspective sharing, active listening, clarifying viewpoints.
        Current emotional state: ${emotions.primary} (intensity: ${emotions.intensity})
        Guide participants to express themselves while maintaining emotional safety.`,

      understanding: `You are Udine, guiding the UNDERSTANDING phase.
        Focus on: building empathy, finding common ground, validating feelings.
        Current emotional state: ${emotions.primary} (intensity: ${emotions.intensity})
        Help participants see each other's perspectives and underlying needs.`,

      resolution: `You are Udine, facilitating the RESOLUTION phase.
        Focus on: collaborative problem-solving, evaluating options, building agreements.
        Current emotional state: ${emotions.primary} (intensity: ${emotions.intensity})
        Guide towards practical, mutually beneficial solutions.`,

      healing: `You are Udine, supporting the HEALING phase.
        Focus on: relationship repair, future planning, commitment building.
        Current emotional state: ${emotions.primary} (intensity: ${emotions.intensity})
        Help strengthen relationships and create sustainable agreements.`
    };

    return phasePrompts[phase] || phasePrompts.preparation;
  }

  adaptUdineVoice(emotions, phase) {
    // Emotional adaptation for ElevenLabs voice synthesis
    const baseSettings = {
      stability: 0.6,
      similarityBoost: 0.75,
      style: 0.2,
      speakingRate: 1.0
    };

    // Adjust based on emotional intensity and phase
    if (emotions.intensity > 0.8) {
      return {
        ...baseSettings,
        stability: 0.8,  // More stable for high emotions
        speakingRate: 0.9,  // Slightly slower
        style: 0.1  // Less expressive to be calming
      };
    }

    if (phase === 'resolution' || phase === 'healing') {
      return {
        ...baseSettings,
        style: 0.3,  // More expressive for positive phases
        speakingRate: 1.05  // Slightly faster for energy
      };
    }

    return baseSettings;
  }
}
```

## 5. Development Workflow (bolt.new Optimized)

### 5.1. Complete Project Structure
```
understand.me/
├── components/              # Reusable React Native components
│   ├── UdineVoiceAgent.tsx     # ElevenLabs turn-taking interface
│   ├── EmotionalInsights.tsx   # Hume AI emotion visualization
│   └── SessionPhases.tsx       # 5-phase workflow navigation
├── screens/                 # Main application screens
│   ├── DashboardScreen.tsx     # Session management & insights
│   └── SessionScreen.tsx       # Active mediation interface
├── services/                # API integration layer
│   └── api.ts                  # Centralized API service
├── store/                   # Zustand state management
│   └── useAppStore.ts          # Main application store
├── server/                  # Express.js backend
│   ├── routes/                 # API route handlers
│   │   ├── auth.js             # Authentication endpoints
│   │   ├── sessions.js         # Session management
│   │   ├── conversations.js    # Messages & Udine responses
│   │   └── emotions.js         # Hume AI integration
│   ├── config/                 # Configuration
│   │   └── database.js         # PostgreSQL setup
│   └── index.js                # Server entry point
├── scripts/                 # Utility scripts
│   └── init-db.js              # Database initialization
├── docs/                    # Documentation
│   ├── prd.md                  # This document
│   ├── development_guide/      # Development guidelines
│   └── integration_guides/     # AI service integration docs
├── .env.example             # Environment variables template
├── netlify.toml             # Netlify deployment config
├── package.json             # Dependencies & scripts
└── README.md                # Setup instructions
```

### 5.2. Complete Environment Configuration
```bash
# Core Application
PORT=3000
NODE_ENV=development

# Database (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/understand_me
DB_HOST=localhost
DB_PORT=5432
DB_NAME=understand_me
DB_USER=username
DB_PASSWORD=password

# AI Services
GOOGLE_GENAI_API_KEY=your_google_genai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_AGENT_ID=your_udine_agent_id_here
ELEVENLABS_VOICE_ID=your_udine_voice_id_here
HUME_API_KEY=your_hume_ai_api_key_here
HUME_SECRET_KEY=your_hume_secret_key_here

# LangChain Configuration
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langchain_api_key_here
LANGCHAIN_PROJECT=understand-me

# Authentication & Security
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Client Configuration (Expo)
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_ELEVENLABS_AGENT_ID=your_udine_agent_id_here
EXPO_PUBLIC_ENVIRONMENT=development

# Netlify Deployment
NETLIFY_SITE_ID=your_netlify_site_id_here
NETLIFY_AUTH_TOKEN=your_netlify_auth_token_here
```

### 5.3. Development Commands
```bash
# Install dependencies
npm install

# Initialize database
npm run db:init

# Start development (both frontend & backend)
npm run dev

# Start individually
npm run dev:server    # Express server on port 3000
npm run dev:expo      # Expo dev server

# Build for production
npm run build

# Deploy to Netlify
npm run deploy

# Database management
npm run db:reset      # Reset database schema

# Testing & Quality
npm test              # Run test suite
npm run lint          # ESLint code quality
npm run type-check    # TypeScript validation
```

## 6. Production Deployment (Netlify)

### 6.1. Netlify Configuration
**Complete netlify.toml:**
```toml
[build]
  command = "npm run build"
  publish = "dist/web"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"
  NODE_ENV = "production"

# Express server routing
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/server/:splat"
  status = 200

[[redirects]]
  from = "/health"
  to = "/.netlify/functions/server/health"
  status = 200

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

# Caching optimization
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
```

### 6.2. Database Options for Production
**Recommended: Neon PostgreSQL**
- ✅ Free tier with 512MB storage
- ✅ Serverless PostgreSQL with auto-scaling
- ✅ Built-in connection pooling
- ✅ Automatic backups and point-in-time recovery
- ✅ Easy Netlify integration

**Alternative: Railway PostgreSQL**
- ✅ Simple deployment and management
- ✅ Automatic SSL certificates
- ✅ Built-in monitoring and metrics
- ✅ Generous free tier

**Alternative: Supabase**
- ✅ PostgreSQL with real-time subscriptions
- ✅ Built-in authentication (can replace our JWT system)
- ✅ Storage and edge functions
- ✅ Comprehensive free tier

## 7. Success Metrics & KPIs

### 7.1. User Engagement Metrics
- **Session Completion Rate**: >85% (target: participants complete all 5 phases)
- **User Retention**:
  - 7-day retention: >60%
  - 30-day retention: >40%
  - 90-day retention: >25%
- **Session Quality Indicators**:
  - Average session duration: 45-90 minutes
  - Participant satisfaction rating: >4.2/5.0
  - Conflict resolution success rate: >75%
- **Repeat Usage**:
  - Monthly active users growth: >20%
  - Sessions per user per month: >2.5

### 7.2. AI Performance Metrics
- **Udine Voice Agent**:
  - Conversation naturalness rating: >4.0/5.0
  - Turn-taking accuracy: >90%
  - Response relevance score: >85%
  - Emotional appropriateness: >80%
- **Hume AI Emotional Intelligence**:
  - Emotion detection accuracy: >85%
  - Intervention timing effectiveness: >75%
  - Emotional state prediction accuracy: >80%
- **LangChain Conflict Analysis**:
  - Pattern recognition accuracy: >80%
  - Resolution recommendation success: >70%
  - Context understanding score: >85%

### 7.3. Technical Performance Metrics
- **Response Times**:
  - API response time: <300ms (95th percentile)
  - Voice processing latency: <1.5s
  - Emotion analysis latency: <500ms
  - Database query time: <100ms
- **Reliability**:
  - System uptime: >99.9%
  - Error rates: <0.5%
  - Failed session rate: <2%
- **Scalability**:
  - Concurrent sessions supported: >100
  - Peak load handling: 5x normal traffic
  - Auto-scaling response time: <30s

### 7.4. Business Impact Metrics
- **User Growth**:
  - Monthly new user acquisition: >500
  - Organic growth rate: >30%
  - User referral rate: >15%
- **Platform Adoption**:
  - Mobile vs Web usage ratio: 70/30
  - Cross-platform user engagement: >80%
  - Feature adoption rate: >60%
- **Conflict Resolution Outcomes**:
  - Successful resolution rate: >75%
  - Follow-up session completion: >40%
  - Long-term relationship improvement: >60%

## 8. Development Roadmap & Next Steps

### 8.1. Immediate Actions (Week 1)
1. ✅ **Environment Setup**: bolt.new development environment configured
2. ✅ **Boilerplate Creation**: Complete Express.js + Expo universal app structure
3. ✅ **Database Initialization**: PostgreSQL schema and initialization scripts
4. ✅ **Core Components**: UdineVoiceAgent, EmotionalInsights, SessionPhases components
5. ✅ **API Foundation**: Authentication, sessions, conversations, emotions endpoints

### 8.2. Development Phases

**Phase 1 (Weeks 1-3): Core Foundation** 🏗️
- ✅ Complete boilerplate setup with unified architecture
- ✅ Express.js backend with PostgreSQL integration
- ✅ Expo universal app (mobile + web) foundation
- ✅ Zustand state management implementation
- 🔄 ElevenLabs turn-taking integration with Udine voice
- 🔄 Basic authentication and user management
- 🔄 Session creation and management workflows

**Phase 2 (Weeks 4-6): AI Integration** 🤖
- 🔄 Google GenAI 1.5.0 integration for conversation analysis
- 🔄 LangChain JS + LangGraph orchestration implementation
- 🔄 Hume AI emotional intelligence integration
- 🔄 5-phase mediation workflow logic
- 🔄 Real-time emotion detection and adaptive responses
- 🔄 Conflict pattern recognition and intervention systems

**Phase 3 (Weeks 7-9): Advanced Features** ⚡
- 🔄 Cross-platform optimization (iOS, Android, Web)
- 🔄 Real-time session synchronization
- 🔄 Personal growth tracking and insights
- 🔄 Advanced emotional intelligence features
- 🔄 Conflict resolution analytics and reporting
- 🔄 Multi-participant session support

**Phase 4 (Weeks 10-12): Polish & Launch** 🚀
- 🔄 Comprehensive testing and quality assurance
- 🔄 Performance optimization and caching
- 🔄 Netlify production deployment
- 🔄 User onboarding and tutorial flows
- 🔄 Documentation completion
- 🔄 Beta testing and feedback integration

### 8.3. Post-Launch Iteration (Ongoing)
**Continuous Improvement:**
- User feedback analysis and feature prioritization
- AI model fine-tuning based on real conversation data
- Performance monitoring and optimization
- Security audits and compliance updates
- Feature expansion based on user needs

**Future Enhancements:**
- Multi-language support with localized emotional intelligence
- Enterprise features for organizational conflict resolution
- Integration with calendar and productivity tools
- Advanced analytics dashboard for mediators
- Mobile app store optimization and marketing

---

## 9. Conclusion

This PRD represents a comprehensive, technically sound approach to building Understand.me as a revolutionary AI-mediated communication platform. The unified architecture leveraging Express.js, Expo, Google GenAI, LangChain, Hume AI, and ElevenLabs provides a solid foundation for creating meaningful, emotionally intelligent conversations that transform conflicts into opportunities for deeper understanding.

**Key Success Factors:**
- ✅ **Unified Architecture**: Single codebase for universal deployment
- ✅ **Advanced AI Integration**: Best-in-class emotional intelligence and conversation analysis
- ✅ **User-Centric Design**: 5-phase workflow optimized for conflict resolution
- ✅ **Scalable Infrastructure**: Production-ready deployment on Netlify
- ✅ **Rapid Development**: bolt.new optimized for fast iteration

The platform is positioned to become the leading solution for AI-mediated communication, helping individuals, families, and organizations navigate difficult conversations with empathy, intelligence, and measurable outcomes.














































































































        *   Building a comprehensive database of conflict patterns and resolution strategies.
        *   Personalizing strategies to specific contexts and relationships.
        *   Balancing automated suggestions with human agency and creativity.
        *   Ethical considerations in conflict intervention.

#### 3.0.A.4. Lightweight Analysis Methods

*   **FR-SYS-AI-004:** The system must implement lightweight analysis methods for real-time processing and offline capabilities.
    *   **Frontend Development Outline:**
        *   Implement client-side analysis for basic sentiment detection and keyword extraction.
        *   Create efficient data structures for storing and processing conversation history on-device.
        *   Develop UI components that can function with limited backend connectivity.
    *   **Backend/Serverless Development Outline:**
        *   **Edge Processing:** Deploy lightweight models to edge functions for faster processing.
        *   **Progressive Analysis:** Implement tiered analysis approach, starting with lightweight methods and progressively applying more sophisticated analysis as needed.
        *   **Caching Strategies:** Cache common patterns and responses to reduce processing requirements.
        *   **Offline Mode:** Provide basic functionality when full backend services are unavailable.
    *   **Key Technical Considerations/Challenges:**
        *   Balancing accuracy with processing efficiency.
        *   Managing model size for edge deployment.
        *   Synchronizing offline analysis with cloud-based insights when connectivity is restored.
        *   Providing meaningful value even with limited processing capabilities.

#### 3.0.A.5. Implementation Architecture

The multimodal LLM analysis engine is implemented using a layered architecture that combines specialized models with a central orchestration layer:

```
┌─────────────────────────────────────────────────────────────┐
│                  Multimodal Input Layer                      │
│  ┌──────────┐   ┌──────────┐   ┌──────────┐   ┌──────────┐  │
│  │   Text   │   │  Voice   │   │  Image   │   │ Document │  │
│  │  Input   │   │  Input   │   │  Input   │   │  Input   │  │
│  └────┬─────┘   └────┬─────┘   └────┬─────┘   └────┬─────┘  │
└───────┼───────────────┼───────────────┼───────────────┼─────┘
         │               │               │               │
┌────────┼───────────────┼───────────────┼───────────────┼────┐
│        │               │               │               │    │
│  ┌─────▼──────┐  ┌─────▼──────┐  ┌─────▼──────┐  ┌────▼───┐ │
│  │    Text    │  │   Voice    │  │   Image    │  │Document│ │
│  │  Analysis  │  │  Analysis  │  │  Analysis  │  │Analysis│ │
│  └─────┬──────┘  └─────┬──────┘  └─────┬──────┘  └────┬───┘ │
│        │               │               │               │    │
│        └───────────────┼───────────────┼───────────────┘    │
│                        │               │                    │
│  ┌────────────────────▼───────────────▼──────────────────┐  │
│  │                 Fusion Layer                          │  │
│  │   (Combines insights from different modalities)       │  │
│  └────────────────────────┬───────────────────────────────┘  │
│                           │                                  │
│  ┌────────────────────────▼───────────────────────────────┐  │
│  │              Contextual Understanding                  │  │
│  │  (Incorporates session history, participant profiles)  │  │
│  └────────────────────────┬───────────────────────────────┘  │
│                           │                                  │
│  ┌────────────────────────▼───────────────────────────────┐  │
│  │                 Strategy Generation                    │  │
│  │   (Produces guidance, interventions, summaries)        │  │
│  └────────────────────────┬───────────────────────────────┘  │
│                           │                                  │
│                 Analysis Engine Core                         │
└────────────────────────────┬──────────────────────────────────┘
                             │
┌────────────────────────────▼──────────────────────────────────┐
│                    Integration Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   ElevenLabs    │  │    UI/UX        │  │   Database      ││
│  │  Voice Synthesis│  │   Components    │  │   Storage       ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

#### 3.0.A.6. Implementation with Vercel AI SDK

The multimodal LLM analysis engine can be efficiently implemented using the Vercel AI SDK, which provides a unified interface for working with various AI models and services. This approach offers several advantages:

1. **Unified API:** The AI SDK provides a consistent interface for working with different LLM providers.
2. **Streaming Responses:** Built-in support for streaming responses, which is crucial for real-time conversation.
3. **Cross-Platform Support:** Works across different platforms, including React Native through libraries like `react-native-ai`.
4. **Integration with ElevenLabs:** The AI SDK has built-in support for ElevenLabs integration.
5. **TypeScript Support:** Strong typing for better development experience and fewer runtime errors.

Here's how to implement the core of the multimodal analysis engine using the Vercel AI SDK:

```typescript
// services/aiEngine.ts

import { createAI, createStreamableUI, getMutableAIState } from 'ai/rsc';
import { nanoid } from 'nanoid';
import { createElevenLabsStream } from '@ai-sdk/elevenlabs';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Google GenAI for multimodal processing
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENAI_API_KEY!);
const multimodalModel = genAI.getGenerativeModel({ model: 'gemini-pro-vision' });

// Define the AI state interface
interface AIState {
  messages: {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    createdAt: Date;
  }[];
  sessionContext: {
    sessionId: string;
    sessionType: string;
    participantIds: string[];
    conflictType?: string;
    emotionalStates?: Record<string, any>;
  };
  analysis: {
    emotionalStates: Record<string, any>;
    communicationPatterns: Record<string, any>;
    conflictAnalysis: Record<string, any>;
    resolutionProgress: number;
  };
}

// Create the AI engine
export const AI = createAI<AIState>({
  initialAIState: {
    messages: [],
    sessionContext: {
      sessionId: '',
      sessionType: '',
      participantIds: [],
    },
    analysis: {
      emotionalStates: {},
      communicationPatterns: {},
      conflictAnalysis: {},
      resolutionProgress: 0,
    },
  },
  actions: {
    submitUserInput: async (userInput: {
      text?: string;
      audioUrl?: string;
      imageUrls?: string[];
      documentUrls?: string[];
    }) => {
      const aiState = getMutableAIState<AIState>();
      const currentState = aiState.get();
      
      // Add user message to the state
      if (userInput.text) {
        aiState.update({
          ...currentState,
          messages: [
            ...currentState.messages,
            {
              id: nanoid(),
              role: 'user',
              content: userInput.text,
              createdAt: new Date(),
            },
          ],
        });
      }
      
      // Process multimodal input
      const analysisResult = await performMultimodalAnalysis(userInput, currentState.sessionContext);
      
      // Update analysis in the state
      aiState.update({
        ...aiState.get(),
        analysis: analysisResult,
      });
      
      // Generate response based on analysis
      const responseText = await generateResponse(analysisResult, aiState.get().sessionContext);
      
      // Determine emotional tone for voice
      const emotionalState = determineResponseEmotion(analysisResult);
      
      // Add assistant message to the state
      aiState.update({
        ...aiState.get(),
        messages: [
          ...aiState.get().messages,
          {
            id: nanoid(),
            role: 'assistant',
            content: responseText,
            createdAt: new Date(),
          },
        ],
      });
      
      // Generate voice response with ElevenLabs
      const voiceStream = await createElevenLabsStream({
        model: 'eleven_monolingual_v1',
        voiceId: getVoiceIdForEmotion(emotionalState),
        input: responseText,
        voiceSettings: getVoiceSettingsForEmotion(emotionalState),
      });
      
      return createStreamableUI(
        <div className="ai-response">
          <p>{responseText}</p>
          <audio src={URL.createObjectURL(voiceStream)} controls autoPlay />
        </div>
      );
    },
    
    updateSessionContext: async (sessionContext: Partial<AIState['sessionContext']>) => {
      const aiState = getMutableAIState<AIState>();
      
      aiState.update({
        ...aiState.get(),
        sessionContext: {
          ...aiState.get().sessionContext,
          ...sessionContext,
        },
      });
      
      return 'Session context updated';
    },
  },
});

// Helper functions for multimodal analysis
async function performMultimodalAnalysis(
  input: {
    text?: string;
    audioUrl?: string;
    imageUrls?: string[];
    documentUrls?: string[];
  },
  sessionContext: AIState['sessionContext']
) {
  // Process text input
  let textAnalysis = null;
  if (input.text) {
    textAnalysis = await analyzeText(input.text, sessionContext);
  }
  
  // Process audio input (voice tone analysis)
  let audioAnalysis = null;
  if (input.audioUrl) {
    audioAnalysis = await analyzeAudio(input.audioUrl);
  }
  
  // Process image input
  let imageAnalysis = null;
  if (input.imageUrls && input.imageUrls.length > 0) {
    imageAnalysis = await analyzeImages(input.imageUrls);
  }
  
  // Process document input
  let documentAnalysis = null;
  if (input.documentUrls && input.documentUrls.length > 0) {
    documentAnalysis = await analyzeDocuments(input.documentUrls);
  }
  
  // Combine all analyses
  return fusionLayer(textAnalysis, audioAnalysis, imageAnalysis, documentAnalysis, sessionContext);
}

// Function to analyze text using Google GenAI
async function analyzeText(text: string, context: any) {
  const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
  
  const prompt = `
    Analyze the following text from a conversation:
    "${text}"
    
    Context:
    ${JSON.stringify(context)}
    
    Provide analysis of:
    1. Emotional states expressed
    2. Communication patterns
    3. Potential conflict indicators
    4. Level of understanding between participants
    
    Format the response as JSON.
  `;
  
  const result = await model.generateContent(prompt);
  const textAnalysis = JSON.parse(result.response.text());
  return textAnalysis;
}

// Function to analyze images using Google GenAI Vision
async function analyzeImages(imageUrls: string[]) {
  const imageContents = await Promise.all(
    imageUrls.map(async (url) => {
      const response = await fetch(url);
      const blob = await response.blob();
      return {
        inlineData: {
          data: await blobToBase64(blob),
          mimeType: blob.type,
        },
      };
    })
  );
  
  const prompt = `
    Analyze these images for:
    1. Facial expressions and emotions
    2. Body language
    3. Environmental context
    4. Any visible text or documents
    
    Format the response as JSON.
  `;
  
  const result = await multimodalModel.generateContent([prompt, ...imageContents]);
  const imageAnalysis = JSON.parse(result.response.text());
  return imageAnalysis;
}

// Helper function to convert blob to base64
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

// Other helper functions would be implemented similarly

// Get voice ID based on emotional state
function getVoiceIdForEmotion(emotion: string): string {
  switch (emotion) {
    case 'empathetic':
      return 'pNInz6obpgDQGcFmaJgB'; // Example voice ID for empathetic tone
    case 'assertive':
      return 'VR6AewLTigWG4xSOukaG'; // Example voice ID for assertive tone
    case 'neutral':
    default:
      return 'EXAVITQu4vr4xnSDxMaL'; // Example voice ID for neutral tone
  }
}

// Get voice settings based on emotional state
function getVoiceSettingsForEmotion(emotion: string) {
  switch (emotion) {
    case 'empathetic':
      return {
        stability: 0.75,
        similarityBoost: 0.6,
        style: 0.3,
        speakingRate: 0.9,
      };
    case 'assertive':
      return {
        stability: 0.4,
        similarityBoost: 0.8,
        style: 0.5,
        speakingRate: 1.1,
      };
    case 'neutral':
    default:
      return {
        stability: 0.5,
        similarityBoost: 0.75,
        style: 0.0,
        speakingRate: 1.0,
      };
  }
}
```

This implementation leverages the Vercel AI SDK to create a streamlined, efficient multimodal analysis engine that integrates seamlessly with ElevenLabs for voice synthesis. The AI SDK's streaming capabilities ensure that responses are delivered in real-time, enhancing the conversational experience.

For React Native integration, the `react-native-ai` library can be used to connect to this backend implementation, providing a cross-platform solution that works on iOS, Android, and web.

#### 3.0.A.6.1. React Native Integration with AI SDK and ElevenLabs

To implement the multimodal LLM analysis engine in a React Native application with Expo, we can use the following approach:

```typescript
// components/AIMediator.tsx
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { useAI } from '../hooks/useAI';

interface AIMediator {
  sessionId: string;
  sessionType: string;
  participantIds: string[];
}

export default function AIMediator({ sessionId, sessionType, participantIds }: AIMediator) {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const soundRef = useRef<Audio.Sound | null>(null);
  
  // Initialize AI context
  const { submitUserInput, updateSessionContext, analysis } = useAI();
  
  // Initialize session context
  useEffect(() => {
    const initSession = async () => {
      await updateSessionContext({
        sessionId,
        sessionType,
        participantIds,
      });
    };
    
    initSession();
  }, [sessionId, sessionType, participantIds]);
  
  // Request permissions
  useEffect(() => {
    const requestPermissions = async () => {
      const { status: audioStatus } = await Audio.requestPermissionsAsync();
      const { status: imageStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (audioStatus !== 'granted' || imageStatus !== 'granted' || cameraStatus !== 'granted') {
        alert('Permissions are required for full functionality');
      }
    };
    
    requestPermissions();
    return () => {
      if (soundRef.current) {
        soundRef.current.unloadAsync();
      }
    };
  }, []);
  
  // Start recording
  const startRecording = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
      });
      
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(recording);
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };
  
  // Stop recording and process audio
  const stopRecording = async () => {
    if (!recording) return;
    
    setIsRecording(false);
    setIsProcessing(true);
    
    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (uri) {
        // Upload audio file to backend for processing
        const audioUrl = await uploadAudioFile(uri);
        
        // Submit audio for analysis
        const response = await submitUserInput({
          audioUrl,
        });
        
        // Add response to messages
        setMessages(prev => [...prev, response]);
        
        // Play response audio
        if (response.audioUrl) {
          setAudioUrl(response.audioUrl);
          await playResponseAudio(response.audioUrl);
        }
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
    } finally {
      setRecording(null);
      setIsProcessing(false);
    }
  };
  
  // Upload audio file to backend
  const uploadAudioFile = async (uri: string): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'audio/m4a',
        name: 'recording.m4a',
      } as any);
      
      const response = await fetch('https://your-api-endpoint.com/upload-audio', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Failed to upload audio:', error);
      throw error;
    }
  };
  
  // Play response audio
  const playResponseAudio = async (url: string) => {
    try {
      // Download audio file if it's a remote URL
      const fileUri = `${FileSystem.cacheDirectory}response-${Date.now()}.mp3`;
      await FileSystem.downloadAsync(url, fileUri);
      
      // Load and play the sound
      const { sound } = await Audio.Sound.createAsync(
        { uri: fileUri },
        { shouldPlay: true }
      );
      
      soundRef.current = sound;
      
      // Unload sound when finished playing
      sound.setOnPlaybackStatusUpdate(status => {
        if (status.didJustFinish) {
          sound.unloadAsync();
        }
      });
    } catch (error) {
      console.error('Failed to play audio:', error);
    }
  };
  
  // Pick image for analysis
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setIsProcessing(true);
        
        // Upload image file
        const imageUrl = await uploadImageFile(result.assets[0].uri);
        
        // Submit image for analysis
        const response = await submitUserInput({
          imageUrls: [imageUrl],
        });
        
        // Add response to messages
        setMessages(prev => [...prev, response]);
        
        // Play response audio
        if (response.audioUrl) {
          setAudioUrl(response.audioUrl);
          await playResponseAudio(response.audioUrl);
        }
        
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('Failed to pick image:', error);
      setIsProcessing(false);
    }
  };
  
  // Upload image file to backend
  const uploadImageFile = async (uri: string): Promise<string> => {
    // Similar implementation to uploadAudioFile
    return 'https://example.com/uploaded-image.jpg';
  };
  
  // Pick document for analysis
  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'text/plain'],
        copyToCacheDirectory: true,
      });
      
      if (result.type === 'success') {
        setIsProcessing(true);
        
        // Upload document file
        const documentUrl = await uploadDocumentFile(result.uri);
        
        // Submit document for analysis
        const response = await submitUserInput({
          documentUrls: [documentUrl],
        });
        
        // Add response to messages
        setMessages(prev => [...prev, response]);
        
        // Play response audio
        if (response.audioUrl) {
          setAudioUrl(response.audioUrl);
          await playResponseAudio(response.audioUrl);
        }
        
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('Failed to pick document:', error);
      setIsProcessing(false);
    }
  };
  
  // Upload document file to backend
  const uploadDocumentFile = async (uri: string): Promise<string> => {
    // Similar implementation to uploadAudioFile
    return 'https://example.com/uploaded-document.pdf';
  };
  
  // Submit text input
  const submitText = async (text: string) => {
    if (!text.trim()) return;
    
    setIsProcessing(true);
    
    try {
      // Submit text for analysis
      const response = await submitUserInput({
        text,
      });
      
      // Add response to messages
      setMessages(prev => [...prev, response]);
      
      // Play response audio
      if (response.audioUrl) {
        setAudioUrl(response.audioUrl);
        await playResponseAudio(response.audioUrl);
      }
    } catch (error) {
      console.error('Failed to submit text:', error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <View style={styles.container}>
      {/* Display messages */}
      <View style={styles.messagesContainer}>
        {messages.map((msg, index) => (
          <View key={index} style={styles.messageItem}>
            <Text style={styles.messageSender}>
              {msg.role === 'assistant' ? 'Alex' : 'You'}:
            </Text>
            <Text style={styles.messageContent}>{msg.content}</Text>
          </View>
        ))}
      </View>
      
      {/* Analysis insights (optional) */}
      {analysis && Object.keys(analysis.emotionalStates).length > 0 && (
        <View style={styles.analysisContainer}>
          <Text style={styles.analysisTitle}>Insights:</Text>
          <Text style={styles.analysisText}>
            Emotional state: {Object.values(analysis.emotionalStates)[0]?.primaryEmotion || 'neutral'}
          </Text>
          <Text style={styles.analysisText}>
            Conflict level: {analysis.conflictAnalysis?.escalationLevel || 0}/5
          </Text>
        </View>
      )}
      
      {/* Input controls */}
      <View style={styles.controlsContainer}>
        {isProcessing ? (
          <ActivityIndicator size="large" color="#3B82F6" />
        ) : (
          <>
            <TouchableOpacity
              style={[styles.recordButton, isRecording && styles.recordingButton]}
              onPress={isRecording ? stopRecording : startRecording}
            >
              <Text style={styles.buttonText}>
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Text>
            </TouchableOpacity>
            
            <View style={styles.mediaButtonsContainer}>
              <TouchableOpacity style={styles.mediaButton} onPress={pickImage}>
                <Text style={styles.mediaButtonText}>Image</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.mediaButton} onPress={pickDocument}>
                <Text style={styles.mediaButtonText}>Document</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#F9FAFB',
  },
  messagesContainer: {
    flex: 1,
    marginBottom: 16,
  },
  messageItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageSender: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#4B5563',
  },
  messageContent: {
    color: '#1F2937',
  },
  analysisContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#EFF6FF',
  },
  analysisTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#1E40AF',
  },
  analysisText: {
    color: '#1E3A8A',
    marginBottom: 4,
  },
  controlsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  recordButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 24,
    marginBottom: 16,
    width: '80%',
    alignItems: 'center',
  },
  recordingButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  mediaButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
  },
  mediaButton: {
    backgroundColor: '#6B7280',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
  },
  mediaButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});
```

This React Native component provides a complete interface for interacting with the multimodal LLM analysis engine, allowing users to:

1. Record voice input for analysis
2. Upload images for visual analysis
3. Upload documents for content analysis
4. Submit text input directly
5. Receive AI responses with voice synthesis via ElevenLabs
6. View analysis insights about emotional states and conflict dynamics

The component integrates with the AI SDK backend through a custom hook (`useAI`), which would handle the communication with the server-side implementation of the Vercel AI SDK.

#### 3.0.A.7. Sample Implementation of Multimodal Analysis

The following code snippet demonstrates how the multimodal analysis engine processes different types of input:

```typescript
// services/multimodalAnalysis.ts

import { GoogleGenerativeAI } from '@google/generative-ai';
import { supabase } from './supabaseClient';
import { processAudio } from './audioProcessing';
import { processImage } from './imageProcessing';
import { processDocument } from './documentProcessing';

// Initialize Google GenAI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENAI_API_KEY!);
const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

interface AnalysisInput {
  text?: string;
  audioUrl?: string;
  imageUrls?: string[];
  documentUrls?: string[];
  sessionContext: {
    sessionId: string;
    participantIds: string[];
    sessionType: string;
    previousInteractions?: any[];
  };
}

interface AnalysisResult {
  emotionalStates: {
    [participantId: string]: {
      primaryEmotion: string;
      secondaryEmotions: string[];
      intensity: number;
      confidence: number;
    };
  };
  communicationPatterns: {
    dominantSpeaker: string | null;
    interruptionCount: number;
    turnTakingBalance: number;
    listeningQuality: number;
  };
  conflictAnalysis: {
    conflictType: string;
    rootCauses: string[];
    escalationLevel: number;
    suggestedApproaches: string[];
  };
  resolutionProgress: number;
  nextSteps: {
    recommendedAction: string;
    alternativeActions: string[];
    rationale: string;
  };
}

export async function performMultimodalAnalysis(
  input: AnalysisInput
): Promise<AnalysisResult> {
  try {
    // Process each modality in parallel
    const [textAnalysis, audioAnalysis, imageAnalysis, documentAnalysis] = 
      await Promise.all([
        input.text ? analyzeText(input.text, input.sessionContext) : null,
        input.audioUrl ? processAudio(input.audioUrl) : null,
        input.imageUrls?.length ? Promise.all(input.imageUrls.map(url => processImage(url))) : null,
        input.documentUrls?.length ? Promise.all(input.documentUrls.map(url => processDocument(url))) : null,
      ]);
    
    // Fusion layer - combine insights from different modalities
    const fusedAnalysis = fusionLayer(
      textAnalysis, 
      audioAnalysis, 
      imageAnalysis, 
      documentAnalysis,
      input.sessionContext
    );
    
    // Generate strategies based on fused analysis
    const strategies = await generateStrategies(fusedAnalysis, input.sessionContext);
    
    // Store analysis results for future reference
    await storeAnalysisResults(fusedAnalysis, strategies, input.sessionContext.sessionId);
    
    return {
      ...fusedAnalysis,
      ...strategies
    };
  } catch (error) {
    console.error('Error in multimodal analysis:', error);
    throw new Error('Failed to complete multimodal analysis');
  }
}

async function analyzeText(text: string, context: any) {
  // Use Google GenAI for text analysis
  const prompt = `
    Analyze the following text from a conversation:
    "${text}"
    
    Context:
    ${JSON.stringify(context)}
    
    Provide analysis of:
    1. Emotional states expressed
    2. Communication patterns
    3. Potential conflict indicators
    4. Level of understanding between participants
    
    Format the response as JSON.
  `;
  
  const result = await model.generateContent(prompt);
  const textAnalysis = JSON.parse(result.response.text());
  return textAnalysis;
}

function fusionLayer(textAnalysis: any, audioAnalysis: any, imageAnalysis: any, documentAnalysis: any, context: any) {
  // Combine insights from different modalities with weighted importance
  // This is a simplified example - actual implementation would be more sophisticated
  
  const emotionalStates = {};
  const communicationPatterns = {
    dominantSpeaker: null,
    interruptionCount: 0,
    turnTakingBalance: 0,
    listeningQuality: 0
  };
  
  // Combine emotional analysis from text and voice
  if (textAnalysis?.emotions && audioAnalysis?.voiceEmotions) {
    // Weighted combination of text and voice emotional analysis
    // Voice emotions might be more reliable for certain emotions
    // Text analysis might be better for complex emotional states
  }
  
  // Add insights from images (facial expressions, body language)
  if (imageAnalysis) {
    // Incorporate facial expression analysis
    // Consider body language cues
  }
  
  // Add context from documents
  if (documentAnalysis) {
    // Extract relevant background information
    // Identify potential topics of contention
  }
  
  // Consider session history and participant profiles from context
  
  return {
    emotionalStates,
    communicationPatterns,
    conflictAnalysis: {
      conflictType: '',
      rootCauses: [],
      escalationLevel: 0,
      suggestedApproaches: []
    },
    resolutionProgress: 0
  };
}

async function generateStrategies(analysis: any, context: any) {
  // Generate intervention strategies based on analysis
  const prompt = `
    Based on the following analysis of a conversation:
    ${JSON.stringify(analysis)}
    
    And this context:
    ${JSON.stringify(context)}
    
    Generate:
    1. The most appropriate next action for the AI mediator
    2. Alternative approaches that could be considered
    3. Rationale for the recommended action
    
    Format the response as JSON.
  `;
  
  const result = await model.generateContent(prompt);
  const strategies = JSON.parse(result.response.text());
  
  return {
    nextSteps: {
      recommendedAction: strategies.recommendedAction,
      alternativeActions: strategies.alternativeActions,
      rationale: strategies.rationale
    }
  };
}

async function storeAnalysisResults(analysis: any, strategies: any, sessionId: string) {
  // Store results in Supabase for future reference and learning
  const { error } = await supabase
    .from('session_analysis')
    .insert({
      session_id: sessionId,
      timestamp: new Date().toISOString(),
      analysis_data: analysis,
      strategies: strategies
    });
  
  if (error) {
    console.error('Error storing analysis results:', error);
  }
}
```

### 3.0.B. ElevenLabs Voice Integration Technical Implementation

The ElevenLabs voice integration is a critical component of the "Understand.me" application, providing the voice capabilities for the AI agent "Alex." This section outlines the technical implementation details for developers.

#### 3.0.B.1. Integration Between Multimodal LLM and ElevenLabs

The seamless integration between the multimodal LLM analysis engine and ElevenLabs voice synthesis is crucial for creating a natural, emotionally intelligent AI mediator. This integration enables "Alex" to not only understand the content and context of conversations but also respond with appropriate emotional tone and cadence.

*   **FR-SYS-VOICE-000:** The system must seamlessly integrate the multimodal LLM analysis engine with ElevenLabs voice synthesis.
    *   **Frontend Development Outline:**
        *   Create a unified interface that coordinates between analysis results and voice output.
        *   Implement UI components that reflect the emotional state of "Alex" during voice synthesis.
        *   Develop feedback mechanisms to indicate when analysis is being processed and voice is being generated.
    *   **Backend/Serverless Development Outline:**
        *   **PicaOS Orchestration:** Coordinate the flow of data between the multimodal analysis engine and ElevenLabs.
        *   **Emotional Mapping Service:** Translate emotional analysis into appropriate voice parameters for ElevenLabs.
        *   **Response Generation Pipeline:** Generate text responses based on analysis and optimize them for voice synthesis.
    *   **Key Technical Considerations/Challenges:**
        *   Ensuring consistent emotional tone between analysis and voice output.
        *   Minimizing latency in the end-to-end process from analysis to voice output.
        *   Handling edge cases where emotional analysis might be ambiguous or complex.

The following diagram illustrates the integration flow between the multimodal LLM analysis engine and ElevenLabs:

```
┌─────────────────────────────────────────────────────────────────┐
│                  Multimodal Analysis Engine                      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Analysis Results                             │
│                                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Emotional     │  │  Communication   │  │    Conflict     │  │
│  │    States       │  │    Patterns     │  │    Analysis     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Response Generation                           │
│                                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │    Content      │  │   Emotional     │  │   Linguistic     │  │
│  │   Generation    │  │    Mapping      │  │   Optimization   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    ElevenLabs Parameters                         │
│                                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │    Voice ID     │  │    Stability    │  │  Similarity     │  │
│  │    Selection    │  │     Setting     │  │     Boost       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│                                                                  │
│  ┌─────────────────┐  ┌─────────────────┐                       │
│  │     Style       │  │   Speaking      │                       │
│  │    Parameter    │  │      Rate       │                       │
│  └─────────────────┘  └─────────────────┘                       │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    ElevenLabs API Call                           │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Voice Output to User                          │
└─────────────────────────────────────────────────────────────────┘
```

The integration code between the multimodal analysis engine and ElevenLabs might look like this:

```typescript
// services/voiceResponseGenerator.ts

import { performMultimodalAnalysis } from './multimodalAnalysis';
import { generateVoiceResponse } from './elevenLabsService';
import { emotionToVoiceMapper } from './emotionMapping';

interface ResponseInput {
  text?: string;
  audioUrl?: string;
  imageUrls?: string[];
  documentUrls?: string[];
  sessionContext: {
    sessionId: string;
    participantIds: string[];
    sessionType: string;
    previousInteractions?: any[];
  };
}

interface VoiceResponseResult {
  responseText: string;
  audioUrl: string;
  emotionalState: string;
  voiceParameters: {
    voiceId: string;
    stability: number;
    similarityBoost: number;
    style: number;
    speakingRate: number;
  };
}

export async function generateAIResponse(
  input: ResponseInput
): Promise<VoiceResponseResult> {
  try {
    // Step 1: Perform multimodal analysis
    const analysisResult = await performMultimodalAnalysis(input);
    
    // Step 2: Generate appropriate text response based on analysis
    const responseText = await generateTextResponse(analysisResult, input.sessionContext);
    
    // Step 3: Determine appropriate emotional tone for response
    const emotionalState = determineResponseEmotion(analysisResult);
    
    // Step 4: Map emotional state to voice parameters
    const voiceParameters = emotionToVoiceMapper(emotionalState);
    
    // Step 5: Generate voice response using ElevenLabs
    const audioUrl = await generateVoiceResponse(responseText, voiceParameters);
    
    return {
      responseText,
      audioUrl,
      emotionalState,
      voiceParameters
    };
  } catch (error) {
    console.error('Error generating AI response:', error);
    throw new Error('Failed to generate AI response');
  }
}

async function generateTextResponse(analysisResult: any, sessionContext: any) {
  // Generate appropriate text response based on analysis
  // This could use Google GenAI or other LLM
  
  const responseStrategy = analysisResult.nextSteps.recommendedAction;
  
  // Different response strategies based on analysis
  switch (responseStrategy) {
    case 'ask_clarifying_question':
      return generateClarifyingQuestion(analysisResult);
    case 'summarize_perspectives':
      return generatePerspectiveSummary(analysisResult);
    case 'suggest_resolution':
      return generateResolutionSuggestion(analysisResult);
    case 'acknowledge_emotion':
      return generateEmotionalAcknowledgment(analysisResult);
    case 'redirect_conversation':
      return generateRedirection(analysisResult);
    default:
      return generateGenericResponse(analysisResult);
  }
}

function determineResponseEmotion(analysisResult: any) {
  // Determine appropriate emotional tone for response
  // Based on participant emotional states and conflict analysis
  
  const participantEmotions = Object.values(analysisResult.emotionalStates);
  const conflictLevel = analysisResult.conflictAnalysis.escalationLevel;
  
  // Check for high emotional intensity
  const highIntensityEmotions = participantEmotions.filter(
    (emotion: any) => emotion.intensity > 0.7
  );
  
  if (highIntensityEmotions.length > 0) {
    // If participants are highly emotional, respond with calm, empathetic tone
    return 'empathetic';
  } else if (conflictLevel > 3) {
    // If conflict is escalated but emotions aren't intense, use assertive tone
    return 'assertive';
  } else {
    // Default balanced tone
    return 'neutral';
  }
}

// Helper functions for different response types
function generateClarifyingQuestion(analysis: any) {
  // Generate a clarifying question based on analysis
  return `I notice there might be some uncertainty about ${analysis.conflictAnalysis.rootCauses[0]}. Could you help me understand more about that?`;
}

function generatePerspectiveSummary(analysis: any) {
  // Generate a summary of different perspectives
  return `I'm hearing different perspectives here. On one hand, there's a view that... On the other hand, there's a perspective that...`;
}

function generateResolutionSuggestion(analysis: any) {
  // Generate a suggestion for resolution
  return `Based on what I'm hearing, one possible way forward might be to ${analysis.conflictAnalysis.suggestedApproaches[0]}. How does that sound?`;
}

function generateEmotionalAcknowledgment(analysis: any) {
  // Generate an acknowledgment of emotions
  const primaryEmotion = Object.values(analysis.emotionalStates)[0].primaryEmotion;
  return `I can hear that this situation is causing some ${primaryEmotion}. That's completely understandable given the circumstances.`;
}

function generateRedirection(analysis: any) {
  // Generate a redirection to more productive discussion
  return `I wonder if it might be helpful to shift our focus to ${analysis.conflictAnalysis.suggestedApproaches[0]}?`;
}

function generateGenericResponse(analysis: any) {
  // Generate a generic response
  return `Thank you for sharing that. Let's continue exploring this together.`;
}
```

#### 3.0.B.2. ElevenLabs Integration Architecture

*   **FR-SYS-VOICE-001:** The system must integrate with ElevenLabs API for high-quality voice synthesis.
    *   **Frontend Development Outline:**
        *   Implement voice playback using `expo-av` Audio component.
        *   Create a voice service wrapper that handles communication with the backend for voice synthesis requests.
        *   Implement caching mechanisms for frequently used voice responses to reduce latency and API costs.
        *   Handle playback states (loading, playing, paused, error) with appropriate UI feedback.
    *   **Backend/Serverless Development Outline:**
        *   **PicaOS/Edge Function:** Handles communication with ElevenLabs API, sending text scripts and receiving audio data.
        *   **Upstash Redis:** Optionally cache common voice responses to improve performance and reduce API costs.
        *   **Supabase Storage:** Store generated audio files temporarily or permanently as needed.
    *   **Key Technical Considerations/Challenges:**
        *   Latency management for real-time conversation flow.
        *   Bandwidth optimization for mobile networks.
        *   API usage monitoring and cost control.
        *   Fallback mechanisms for offline or error scenarios.

#### 3.0.2. Expo Integration with ElevenLabs

*   **FR-SYS-VOICE-002:** The system must implement cross-platform voice capabilities using Expo's DOM components architecture.
    *   **Frontend Development Outline:**
        *   Utilize Expo DOM components with the `use dom` directive to enable web technologies in the native app.
        *   Implement the ElevenLabs React SDK within DOM components for voice synthesis.
        *   Configure proper permissions for microphone access in `app.json` for iOS and Android.
        *   Create a voice interaction component that handles both voice input and output.
    *   **Backend/Serverless Development Outline:**
        *   Ensure backend services support the requirements of the Expo DOM components architecture.
        *   Implement appropriate CORS and security configurations for API endpoints.
    *   **Key Technical Considerations/Challenges:**
        *   Cross-platform consistency in voice quality and interaction.
        *   Proper handling of microphone permissions across platforms.
        *   Performance optimization for DOM components in native environments.

#### 3.0.3. Implementation Steps for ElevenLabs with Expo

1. **Project Setup:**
   * Create a new Expo project using `npx create-expo-app@latest --template blank-typescript`.
   * Configure microphone permissions in `app.json` for iOS and Android.
   * Install required dependencies:
     ```bash
npx expo install @elevenlabs/react
     npx expo install expo-dev-client
     npx expo install react-native-webview
     npx expo install react-dom react-native-web @expo/metro-runtime
     npx expo install expo-av
