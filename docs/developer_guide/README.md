# Understand.me - <PERSON><PERSON><PERSON>'s Guide

**Version**: 1.1.0  
**Last Updated**: 2025-06-25

Welcome to the "Understand.me" Developer's Guide. This guide is specifically for software engineers, architects, and technical leads involved in the hands-on development and maintenance of the "Understand.me" mobile application and its associated backend systems.

It focuses on the technical architecture, implementation details, development processes, and operational considerations for building "Understand.me" using its serverless-focused technology stack, which includes Expo (React Native), Supabase, AI Orchestration Layer (AI Orchestration), Google GenAI, ElevenLabs, Dappier, Nodely, and Sentry.

This Developer's Guide complements the separate **UI Development Guide** (located in the `../development_guide` directory), which details the UI/UX principles, screen-by-screen breakdowns, shared UI components, and <PERSON>dine's persona from a user interface perspective. Developers should refer to both guides for a complete understanding of the application.

## Table of Contents

1.  [Part 1: Introduction & System Overview](part1_introduction_system_overview.md)
2.  [Part 2: Development Environment Setup](part2_development_environment_setup.md)
3.  [Part 3: Core Backend & Data Management (Supabase)](part3_core_backend_data_management_supabase.md)
4.  [Part 4: AI Orchestration & Core Logic (AI Orchestration Layer & Google GenAI)](part4_ai_orchestration_core_logic_picaos_google_genai.md)
5.  [Part 5: External Service Integrations (Detailed)](part5_external_service_integrations_detailed.md)
6.  [Part 6: Feature Implementation Guide](part6_feature_implementation_guide.md)
7.  [Part 7: Expo (React Native) Best Practices](part7_expo_react_native_best_practices.md)
8.  [Part 8: Testing Strategies for Developers](part8_testing_strategies_developer.md)
9.  [Part 9: Deployment & Operations for Developers](part9_deployment_operations_developer.md)

---
*For UI specific designs, flows and components, please refer to the [UI Development Guide](../development_guide/README.md).*
*The System Prompt for the UI Development AI Agent can be found at [System Prompt UI Agent](../system_prompt_ui_agent.md).*
