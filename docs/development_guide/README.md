# Understand.me - Development Guide

This document serves as the comprehensive guide for developing the "Understand.me" AI-mediated conflict resolution platform. It outlines the unified architecture, development workflows, and implementation patterns for the 5-phase mediation system with Udine voice agent.

## 🏗️ Core Architecture Documents

- **[Unified Architecture Specification](../unified_architecture_specification.md)** - Definitive tech stack and structure
- **[bolt.new Development Rules](../bolt-development-rules.md)** - Development workflows and best practices
- **[Boilerplate Setup Guide](boilerplate_setup_guide.md)** - Complete setup instructions

## 🔧 Integration Guides

- **[LangChain 5-Phase Mediation Workflow](../integration_guides/langchain_mediation_workflow.md)** - AI orchestration implementation
- **[Hume AI Emotional Intelligence](../integration_guides/hume_ai_emotional_intelligence.md)** - Real-time emotion analysis

## 📱 UI Development Guide (Parts 1-10)

1.  [Part 1: Global Design & Interaction Principles](part1_global_design.md) ✅ *Updated for Udine agent*
2.  [Part 2: Initial User Experience & Onboarding](part2_initial_user_experience.md) ⚠️ *Needs update*
3.  [Part 3: Main Dashboard & Core Navigation](part3_main_dashboard_core_navigation.md) ⚠️ *Needs update*
4.  [Part 4: Host Path - Initiating a Session](part4_host_path_initiating_session.md) ⚠️ *Needs update*
5.  [Part 5: Participant Path - Joining a Session](part5_participant_path_joining_session.md) ⚠️ *Needs update*
6.  [Part 6: Pre-Session Preparation (Converged Path)](part6_pre_session_preparation_converged_path.md) ⚠️ *Needs update*
7.  [Part 7: AI-Mediated Session Interface (The Five Phases)](part7_ai_mediated_session_interface.md) ⚠️ *Needs update*
8.  [Part 8: Post-Session & Follow-Up](part8_post_session_follow_up.md) ⚠️ *Needs update*
9.  [Part 9: Growth & Tracking Module](part9_growth_tracking_module.md) ⚠️ *Needs update*
10. [Part 10: Shared Components & UI Patterns](part10_shared_components_ui_patterns.md) ⚠️ *Needs update*

## 📋 Validation & Testing

- **[Documentation Validation Checklist](../documentation_validation_checklist.md)** - Consistency verification

---

**Note**: Parts 2-10 of the UI guide need updates to reflect the unified architecture with Express.js backend, LangChain orchestration, Udine voice agent, and Hume AI emotional intelligence. Refer to the core architecture documents above for the current specifications.
