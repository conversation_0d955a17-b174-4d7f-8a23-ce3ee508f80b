# Understand.me - UI Development Guide

This document serves as the comprehensive guide for UI development of the "Understand.me" application. It outlines design principles, user experience flows, core application paths, shared components, and AI agent interactions.

## Table of Contents

1.  [Part 1: Global Design & Interaction Principles](part1_global_design.md)
2.  [Part 2: Initial User Experience & Onboarding](part2_initial_user_experience.md)
3.  [Part 3: Main Dashboard & Core Navigation](part3_main_dashboard_core_navigation.md)
4.  [Part 4: Host Path - Initiating a Session](part4_host_path_initiating_session.md)
5.  [Part 5: Participant Path - Joining a Session](part5_participant_path_joining_session.md)
6.  [Part 6: Pre-Session Preparation (Converged Path)](part6_pre_session_preparation_converged_path.md)
7.  [Part 7: AI-Mediated Session Interface (The Five Phases)](part7_ai_mediated_session_interface.md)
8.  [Part 8: Post-Session & Follow-Up](part8_post_session_follow_up.md)
9.  [Part 9: Growth & Tracking Module](part9_growth_tracking_module.md)
10. [Part 10: Shared Components & UI Patterns](part10_shared_components_ui_patterns.md)

---
*Also relevant: [System Prompt for UI Development AI Agent](../system_prompt_ui_agent.md)*
