# Understand.me - Development Guide

This document serves as the comprehensive guide for developing the "Understand.me" AI-mediated conflict resolution platform. It outlines the unified architecture, development workflows, and implementation patterns for the 5-phase mediation system with Udine voice agent.

## 🏗️ Core Architecture Documents

- **[Unified Architecture Specification](../unified_architecture_specification.md)** - Definitive tech stack and structure
- **[bolt.new Development Rules](../bolt-development-rules.md)** - Development workflows and best practices
- **[Boilerplate Setup Guide](boilerplate_setup_guide.md)** - Complete setup instructions

## 🔧 Integration Guides

- **[LangChain 5-Phase Mediation Workflow](../integration_guides/langchain_mediation_workflow.md)** - AI orchestration implementation
- **[Hume AI Emotional Intelligence](../integration_guides/hume_ai_emotional_intelligence.md)** - Real-time emotion analysis

## 📱 UI Development Guide

### ✅ Current Documentation (Updated for Unified Architecture)

1. **[Part 1: Global Design & Interaction Principles](part1_global_design.md)**
   - ✅ Updated for Udine voice agent
   - ✅ Unified technology stack
   - ✅ 5-phase mediation workflow principles

### 🚧 Future UI Documentation (To Be Created)

The following UI documentation will be created based on the unified architecture:

- **Part 2: Initial User Experience & Onboarding** - AI-powered onboarding with Udine
- **Part 3: Main Dashboard & Core Navigation** - 5-phase workflow navigation
- **Part 4: Host Path - Initiating a Session** - LangChain conflict analysis integration
- **Part 5: Participant Path - Joining a Session** - Emotional intelligence onboarding
- **Part 6: Pre-Session Preparation** - LangGraph workflow preparation
- **Part 7: AI-Mediated Session Interface** - 5-phase mediation with Udine
- **Part 8: Post-Session & Follow-Up** - Action items and healing phase
- **Part 9: Growth & Tracking Module** - Emotional intelligence insights
- **Part 10: Shared Components & UI Patterns** - Udine and emotion components

## 📋 Validation & Testing

- **[Documentation Validation Checklist](../documentation_validation_checklist.md)** - Consistency verification

---

**Note**: Parts 2-10 of the UI guide need updates to reflect the unified architecture with Express.js backend, LangChain orchestration, Udine voice agent, and Hume AI emotional intelligence. Refer to the core architecture documents above for the current specifications.
