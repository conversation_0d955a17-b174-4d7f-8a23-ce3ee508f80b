# Part 9: Growth & Tracking Module

This part of the Development Guide details the "Understand-me" Growth & Tracking Module for the **Expo (React Native) mobile application**. It focuses on personalized insights, progress tracking, and learning resources. UI styling uses **React Native StyleSheet API** (or NativeWind) and navigation is via **React Navigation**, likely within a dedicated "Growth" tab on the main Bottom Tab Navigator. AI Orchestration Layer might be involved in orchestrating complex AI insight generation flows, while <PERSON><PERSON><PERSON> could source sensitive user data for insights if stored in a decentralized manner. <PERSON>dely would manage storage/retrieval of user progress data and learning material metadata from Supabase.

## 9.1. Screen: Personal Growth Insights (Mermaid: CB, K, CF, CG)

*   **Mermaid Diagram ID:** K (Personal Growth Dashboard - main screen of the "Growth" tab), CB (Overview/Summary card/section), CF (Communication Pattern detail screen/section), CG (Emotional Regulation detail screen/section).

*   **Purpose:** (Remains the same)

*   **Key UI Elements (within Growth Dashboard - K, using React Native components):**
    *   **Main Container (`<ScrollView>`):**
    *   **View CB: Insights Overview / Dashboard Summary (`<View>` card):**
        *   Personalized greeting from <PERSON> (`<Text>` - Component 10.2).
        *   "Key Highlight" Card (`<View>` with `<Text>`).
        *   Summary widgets (`<TouchableOpacity>` cards) for Communication Patterns, Emotional Regulation, linking to CF, CG.
        *   Trend lines (`react-native-svg-charts`).
        *   Date range selectors (`<TouchableOpacity>` buttons or a date picker modal - Component 10.7).
    *   **View CF: Communication Pattern Insights (Detail screen via Stack Navigation, or collapsible `<View>` section):**
        *   Specific Metric Sections (`<View>` for each, e.g., Clarity, Filler Words):
            *   Metric score/level (`<Text>`). Trend graph (`react-native-svg-charts`).
            *   Comparison to goals (`<Text>`). Examples (`<Text>` with links to transcript moments - potentially opening a Modal with relevant transcript part). Actionable tips (`<Text>`).
    *   **View CG: Emotional Regulation & Expression Insights (Detail screen or collapsible `<View>` section):**
        *   Sentiment Trend Analysis (`react-native-svg-charts`).
        *   Emotional Word Cloud (could be a custom `<View>` rendering styled `<Text>` components, or an `<Image>` generated by AI).
        *   Reflection Prompts (`<Text>`). Progress on Goals (`<Text>`). Tips (`<Text>`).

*   **Voice Agent Interactions (Alex - Component 10.2):** (Scripts remain largely the same, delivered via Alex's standard presence in AI Panel or specific coaching cards)
    *   Alex explains insights displayed in `<Text>` and chart components.
    *   AI Orchestration Layer might be used for complex, on-device analysis of voice recordings (if user consented) to provide richer emotional or tonal insights, which Alex then presents.

*   **Navigation (React Navigation):**
    *   "Growth" tab on main Bottom Tab Navigator leads to K (Personal Growth Dashboard).
    *   CB is the main view. CF and CG might be separate screens in a Stack Navigator initiated from K, or expandable sections within K.
    *   Links to specific session transcripts (Part 3.2 LA).

*   **Multimedia Aspects:**
    *   Interactive Charts (`react-native-svg-charts`). Color-coding (`StyleSheet`). Icons (`<Image>`). Alex's Avatar (`<Image>`/Lottie).

## 9.2. Screen: Achievement Badges & Progress (Mermaid: CC, K, CI)

*   **Mermaid Diagram ID:** K (Growth Dashboard), CC (Badges display section/screen), CI (Progress detail view for badges/skills).

*   **Purpose:** (Remains the same)
    *   Badge criteria and user progress data stored in Supabase, potentially managed by Nodely.

*   **Key UI Elements (within Growth Dashboard - K, using React Native components):**
    *   **View CC: Achievements / Badges Display (`<View>` section or separate screen in Stack):**
        *   "My Badges" Section (`<FlatList horizontal>` or grid of `<Image>` components for badges). Tapping a badge (`<TouchableOpacity>`) opens a Modal (Component 10.7) with description (`<Text>`).
        *   "Badges in Progress" / "Next Badges to Earn" Section (CI - `<View>`):
            *   `<FlatList>` of badges with progress bars (e.g., custom animated `<View>` or library). `<Text>` for percentage/criteria.
        *   "Recently Unlocked" (`<View>` highlighting new `<Image>` badges).
    *   **View CI: Skill Development Progress (Could be part of CC or separate screen):**
        *   `<FlatList>` of skills. Each item: Skill name (`<Text>`), progress bar (`<View>`), link (`<TouchableOpacity>`) to insights (9.1) or resources (9.3).
    *   Social Sharing (`<TouchableOpacity>` using `expo-sharing` API, strictly opt-in).

*   **Voice Agent Interactions (Alex - Component 10.2):** (Scripts remain largely the same, celebratory and encouraging)

*   **Navigation (React Navigation):**
    *   Section within Growth Dashboard (K) or a separate screen in its Stack.
    *   Tapping badges/skills links to details or other relevant sections (9.1, 9.3).

*   **Multimedia Aspects:**
    *   Badge `<Image>` designs. Progress Bars (`<View>` with `StyleSheet` or animation). Celebratory Lottie animations. Alex's Avatar.

## 9.3. Screen: Recommended Resources (Mermaid: CD, K, CH)

*   **Mermaid Diagram ID:** K (Growth Dashboard), CD (Main resources display), CH (Categorization/filtering UI).

*   **Purpose:** (Remains the same)
    *   Resource metadata stored in Supabase, potentially curated/updated via Nodely.

*   **Key UI Elements (within Growth Dashboard - K, using React Native components):**
    *   **View CD: Main Recommended Resources Display (`<View>` or separate screen in Stack):**
        *   "For You" Section (`<View>` with `<FlatList>` of resource cards).
            *   Resource Card (`<TouchableOpacity>`): `<Image>` (thumbnail), `<Text>` (Title, Type, Description). Opens in-app (e.g., `<Modal>` with `<ScrollView>` for articles, `expo-av` for video) or via `Linking` for external resources.
        *   "Browse by Skill" Section (CH - `<View>` with `<TouchableOpacity>` buttons or a Picker for categories, filtering the main `<FlatList>`).
        *   Search/Filter Bar (`<TextInput>`, Pickers).
        *   Resource List Area (`<FlatList>`).
        *   "Saved for Later" / "My Learning List" (`<TouchableOpacity>` icon on cards, list view on a separate screen or filtered view).
        *   "Completed Resources" tracked via user interaction and stored in Supabase.

*   **Voice Agent Interactions (Alex - Component 10.2):** (Scripts remain largely the same, as a learning advisor)

*   **Navigation (React Navigation):**
    *   Section within Growth Dashboard (K) or a separate screen in its Stack.
    *   Resource cards link to internal viewers (Modals, new screens) or external browser.

*   **Multimedia Aspects:**
    *   Resource Thumbnails/Icons (`<Image>`). Embedded `expo-av` player for videos. Alex's Avatar.

## 9.4. Screen: Future Conflict Prevention Insights (Mermaid: CE, K)

*   **Mermaid Diagram ID:** K (Growth Dashboard), CE (Conflict prevention insights display).

*   **Purpose:** (Remains the same)
    *   Complex pattern analysis from multiple sessions (data from Supabase, potentially involving Dappier for cross-user data if anonymized and consented) orchestrated by AI Orchestration Layer or Nodely, results presented by Google GenAI.

*   **Key UI Elements (within Growth Dashboard - K, using React Native components):**
    *   **View CE: Future Conflict Prevention Insights Display (`<View>` or separate screen in Stack):**
        *   Headline (`<Text>`).
        *   "Key Patterns Observed" Section (`<FlatList>` of `<View>` cards, each with `<Text>` pattern description and `<TouchableOpacity>` to see anonymized examples/data in a Modal).
        *   "Proactive Strategies/Recommendations" Section (`<FlatList>` of `<View>` cards with `<Text>` strategy and link (`<TouchableOpacity>`) to resources in 9.3).
        *   "Early Warning Signs" Section (`<FlatList>` of `<Text>` items).
        *   User Feedback (`<TouchableOpacity>` icons).
    *   "Track a New Prevention Goal" Button (`<TouchableOpacity>`).

*   **Voice Agent Interactions (Alex - Component 10.2):** (Scripts remain largely the same, constructive and empowering)

*   **Navigation (React Navigation):**
    *   Section within Growth Dashboard (K). Proactive notifications (Component 10.6) might link here.
    *   Links to Recommended Resources (9.3).

*   **Multimedia Aspects:**
    *   Trend Visualizations (`react-native-svg-charts` for pattern recurrence). Icons (`<Image>`). Alex's Avatar. Illustrative graphics (`<Image>`).

This concludes Part 9.
