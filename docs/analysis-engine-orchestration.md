# 🧠 Analysis Engine – Multi-Step Orchestration

This document explains **how each turn of the conversation is processed** end-to-end using our current stack: **Vercel AI SDK + Google Gemini**, optional Voyage embeddings for RAG, and ElevenLabs for voice. The goal is clarity—not code dumps—so you can picture the flow before writing functions.

> Terminology
> * **Turn** – one full round-trip: user speaks → <PERSON><PERSON> replies.
> * **Engine** – the functions in `services/ai/` that transform voice into a final answer.

---
## 0. Bird’s-Eye Pipeline
```
Voice ► STT ► Enrich ► RAG ► Gemini ► Tools ► TTS
```
1. **STT** – Convert speech to plain text.  
2. **Enrich** – Add speaker info, emotion, and recent history.  
3. **RAG** – Retrieve relevant knowledge snippets.  
4. **Gemini** – Produce the reply + any tool calls.  
5. **Tools** – Execute client-side actions (brightness, etc.).  
6. **TTS** – Speak via ElevenLabs.

Each box is a *function*; chaining them keeps things testable and lets us swap providers later.

---
## 1. Speech-to-Text (STT)
* **Where** `services/ai/stt.ts`  
* **How** `await whisper.transcribe(blob)` (or Google Speech API fallback).
* **Why modular?** Mobile vs web may record different formats; we keep STT isolated.

```ts
export async function speechToText(blob: Blob): Promise<string> {
	// 1. compress if needed 2. send to Whisper 3. return transcript
}
```

---
## 2. Enrichment Layer
Build a `ChatContext` object:
```ts
{
	userId,
	profile: { name, preferences },
	emotion: latestEmotion,  // from Hume
	recent: lastNMessages,
}
```
Why? Gemini answers improve when it sees who’s speaking and recent flow.

---
## 3. Retrieval-Augmented Generation (RAG)
1. Split docs → chunks (`utils/chunker.ts`).
2. `embedMany(chunks)` with Voyage.
3. Store vectors in local IndexedDB.
4. At turn time: `cosineSimilarity(queryEmbed, topK)`.
5. Return top passages; attach to `ChatContext`.

> **Note** Vercel AI SDK ships `embedMany` & `cosineSimilarity` helpers, so no heavy LangChain needed.

---
## 4. Gemini Prompt Assembly
```ts
import { generateStream } from "ai";

const response = await generateStream({
	model: gemini1p5,
	prompt: [
		{role: 'system', content: SYSTEM_PROMPT},
		...context.recent,
		{role: 'assistant', content: passagesMarkup},
		{role: 'user', content: transcript},
	],
	functions: toolSchemas,  // brightness, etc.
});
```
* **SYSTEM_PROMPT** explains Udine personality + tool usage rules.
* `functions` lets Gemini emit tool calls (`{"name":"flash_screen","arguments":{...}}`).
* The stream feeds UI for instant feedback.

---
## 5. Tool Execution
In `services/ai/tools.ts` map each schema to an **actual** client-side function. The UI listens to streamed tool calls and runs them (battery level, screen flash…).

---
## 6. Text-to-Speech (TTS)
* Parse streamed tokens into plain text.
* Send to ElevenLabs `speak()` with Udine voice ID.

Because TTS starts while the tail of the reply is still generating, perceived latency stays low.

---
## 7. Error & Retry Strategy
* Any stage can throw; we surface a toast + keep transcript so user can resend.
* On bad network, STT + RAG still work offline; only Gemini needs connectivity – show offline banner.

---
## 8. Extending the Chain
Want sentiment-aware music? Add a **“Mood Audio”** step *after* emotion detection but *before* TTS. Because everything is modular, you just insert a new function in the chain.

---
## TL;DR
Our analysis engine is a **pipeline of small, single-purpose functions** glued together by Vercel AI SDK streams. This keeps code readable, lets us unit-test each stage, and avoids heavyweight LangChain graphs while still supporting tool-calling, RAG, and voice reply.
