# Understand.me - Development Rules for bolt.new

## 1. Overview

This document defines the development rules and guidelines specifically for building Understand.me in the bolt.new environment. These rules ensure consistency, maintainability, and optimal performance within bolt.new's constraints and capabilities.

## 2. Architecture Principles

### 2.1. Simplified Stack
- **Frontend**: Expo (React Native) for mobile + web
- **Backend**: Express.js/Node.js (traditional server architecture)
- **Database**: PostgreSQL (Neon or similar Netlify-compatible)
- **AI Services**: Google GenAI, LangChain JS, Hume AI, ElevenLabs
- **Deployment**: Netlify free tier

### 2.2. bolt.new Optimization
- Keep dependencies minimal and well-tested
- Use standard npm packages over custom implementations
- Prioritize packages with good TypeScript support
- Avoid complex build configurations

## 3. Project Structure

```
understand-me/
├── src/                    # Frontend (Expo)
│   ├── components/         # Reusable UI components
│   ├── screens/           # Main app screens
│   ├── services/          # API clients and external services
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript definitions
│   └── constants/         # App constants and config
├── server/                # Backend (Express.js)
│   ├── routes/            # API route handlers
│   ├── middleware/        # Express middleware
│   ├── services/          # Business logic services
│   ├── models/            # Database models
│   └── utils/             # Server utilities
├── docs/                  # Documentation
├── package.json           # Dependencies and scripts
└── README.md             # Project overview
```

## 4. Development Guidelines

### 4.1. TypeScript Usage
- Use strict TypeScript configuration
- Define interfaces for all API responses
- Type all component props and state
- Avoid `any` type - use `unknown` if necessary

### 4.2. Component Development
```typescript
// Example component structure
interface ComponentProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

export const CustomButton: React.FC<ComponentProps> = ({
  title,
  onPress,
  disabled = false
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
  },
  disabled: {
    opacity: 0.5,
  },
  text: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
});
```

### 4.3. API Integration
```typescript
// Service layer example
export class ConversationService {
  private baseUrl = process.env.EXPO_PUBLIC_API_URL;

  async createSession(data: CreateSessionRequest): Promise<Session> {
    const response = await fetch(`${this.baseUrl}/api/sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await getAuthToken()}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.statusText}`);
    }

    return response.json();
  }
}
```

## 5. AI Integration Patterns

### 5.1. ElevenLabs Integration
```typescript
// Following the Expo + ElevenLabs guide
import { Conversation } from '@11labs/client';

export const useVoiceConversation = () => {
  const [conversation, setConversation] = useState<Conversation | null>(null);

  const startConversation = async (agentId: string) => {
    const conv = await Conversation.startSession({
      agentId,
      onMessage: (message) => {
        // Handle incoming messages
      },
      onError: (error) => {
        console.error('Conversation error:', error);
      },
    });
    
    setConversation(conv);
  };

  return { conversation, startConversation };
};
```

### 5.2. LangChain JS Orchestration
```typescript
// Server-side AI orchestration
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumeClient } from "hume";

export class AIOrchestrator {
  private llm: ChatGoogleGenerativeAI;
  private hume: HumeClient;

  constructor() {
    this.llm = new ChatGoogleGenerativeAI({
      modelName: "gemini-1.5-pro",
      apiKey: process.env.GOOGLE_GENAI_API_KEY,
    });
    
    this.hume = new HumeClient({
      apiKey: process.env.HUME_API_KEY,
    });
  }

  async processMessage(message: string, context: ConversationContext) {
    // 1. Analyze emotions with Hume AI
    const emotions = await this.analyzeEmotions(message);
    
    // 2. Generate response with Google GenAI
    const response = await this.generateResponse(message, context, emotions);
    
    // 3. Return structured response
    return {
      text: response,
      emotions,
      metadata: { timestamp: new Date().toISOString() }
    };
  }
}
```

## 6. Environment Configuration

### 6.1. Environment Variables
```bash
# Frontend (.env)
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_APP_ENV=development

# Backend (.env)
DATABASE_URL=postgresql://user:pass@host:port/dbname
GOOGLE_GENAI_API_KEY=your_google_genai_key
ELEVENLABS_API_KEY=your_elevenlabs_key
HUME_API_KEY=your_hume_api_key
JWT_SECRET=your_jwt_secret
PORT=3000
```

### 6.2. Package.json Scripts
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:server\" \"npm run dev:expo\"",
    "dev:server": "nodemon server/index.js",
    "dev:expo": "expo start",
    "build": "npm run build:server && npm run build:expo",
    "build:server": "tsc && cp -r server/public dist/",
    "build:expo": "expo build:web",
    "deploy": "netlify deploy --prod"
  }
}
```

## 7. Testing Strategy

### 7.1. Component Testing
```typescript
// Example test with React Native Testing Library
import { render, fireEvent } from '@testing-library/react-native';
import { CustomButton } from '../CustomButton';

describe('CustomButton', () => {
  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <CustomButton title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

### 7.2. API Testing
```typescript
// Example API test
describe('ConversationService', () => {
  it('creates a session successfully', async () => {
    const service = new ConversationService();
    const sessionData = { title: 'Test Session' };
    
    const session = await service.createSession(sessionData);
    
    expect(session).toHaveProperty('id');
    expect(session.title).toBe('Test Session');
  });
});
```

## 8. Performance Guidelines

### 8.1. Optimization Rules
- Use React.memo for expensive components
- Implement proper list virtualization for large datasets
- Optimize images and assets for mobile
- Use lazy loading for non-critical components
- Implement proper error boundaries

### 8.2. Memory Management
- Clean up subscriptions in useEffect cleanup
- Avoid memory leaks in voice processing
- Implement proper WebSocket connection management
- Use proper state management to avoid unnecessary re-renders

## 9. Deployment Checklist

### 9.1. Pre-deployment
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] API endpoints tested
- [ ] Voice integration tested on target devices
- [ ] Error handling implemented
- [ ] Performance optimized

### 9.2. Netlify Deployment
- [ ] netlify.toml configured
- [ ] Build commands working
- [ ] Environment variables set in Netlify dashboard
- [ ] Database connection tested in production
- [ ] AI service API keys validated

## 10. Troubleshooting

### 10.1. Common Issues
- **ElevenLabs connection issues**: Check API key and network connectivity
- **Database connection failures**: Verify DATABASE_URL and network access
- **Expo build failures**: Clear cache with `expo r -c`
- **TypeScript errors**: Run `npx tsc --noEmit` to check types

### 10.2. Debug Tools
- Use Flipper for React Native debugging
- Enable network inspection for API calls
- Use Chrome DevTools for web debugging
- Implement proper logging for production issues

This development guide ensures consistent, maintainable code that works optimally in the bolt.new environment while leveraging the simplified architecture for Understand.me.
