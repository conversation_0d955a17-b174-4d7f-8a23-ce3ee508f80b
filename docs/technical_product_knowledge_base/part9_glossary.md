# Part 9: Glossary of Terms & Acronyms

## Introduction

This glossary defines key terms, technologies, platform-specific concepts, and acronyms used throughout the "Understand.me" documentation (including the UI Development Guide, Developer's Guide, and Technical Product Knowledge Base). Its purpose is to provide a shared understanding and clear definitions for all team members and stakeholders.

---

**A**

*   **Action Plan:** A list of tasks, owners, and due dates identified during the "Resolve" phase of a session and included in the Session Summary.
*   **AI Mediator (Alex):** The AI-powered voice agent in "Understand.me" that facilitates sessions, provides insights, and guides users. (See also: <PERSON>).
*   **Alex:** The persona name for the AI Mediator in "Understand.me."
*   **API (Application Programming Interface):** A set of rules and protocols that allows different software applications to communicate with each other.
*   **API Gateway:** A server that acts as a single entry point for client requests to multiple backend services. AI Orchestration Layer often serves this role for AI-related services in "Understand.me."

**B**

*   **BaaS (Backend-as-a-Service):** A cloud service model that provides backend functionalities like database, authentication, and storage, abstracting away server management. (See also: Supabase).

**C**

*   **CID (Content Identifier):** A unique hash used to identify content on IPFS. Generated by Nodely when files are pinned.
*   **Conversational Personality Assessment:** An onboarding process where users interact with Alex to help the system understand their communication preferences.
*   **CRUD (Create, Read, Update, Delete):** Basic operations for managing data in a persistent store like a database.

**D**

*   **Dappier:** A conceptual service in "Understand.me" potentially used for real-time data feeds, Retrieval Augmented Generation (RAG), or decentralized identity/data solutions.
*   **DID (Decentralized Identifier):** A type of identifier that enables verifiable, decentralized digital identity. Potentially relevant if Dappier is used for advanced identity features.
*   **Developer's Guide (Dev Guide):** The set of documents (of which this TPKB is part) focused on the technical architecture, implementation, and processes for building "Understand.me."

**E**

*   **EAS (Expo Application Services):** A suite of cloud services for Expo and React Native apps, including EAS Build (for building app binaries) and EAS Update (for Over-The-Air updates).
*   **ElevenLabs API:** A third-party service used for high-quality Text-to-Speech (TTS) synthesis, providing the voice for Alex.
*   **Expo:** A framework and platform for universal React applications, used in "Understand.me" to build the cross-platform (iOS and Android) mobile app with React Native and TypeScript.
*   **Expo CLI:** The command-line interface for developing and managing Expo projects.

**F**

*   **The Five Phases:** The structured approach "Understand.me" uses for AI-mediated sessions:
    1.  **Prepare:** Setting the stage, reviewing goals and rules.
    2.  **Express:** Participants share their perspectives without interruption.
    3.  **Understand:** Clarifying perspectives and ensuring mutual understanding, aided by AI summaries.
    4.  **Resolve:** Brainstorming solutions and finding common ground.
    5.  **Heal:** Reflecting on the session and committing to future actions.

**G**

*   **Google GenAI SDK (e.g., Gemini):** The core Large Language Model (LLM) service used for natural language understanding, text analysis, summarization, insight generation, Speech-to-Text (STT), and powering Alex's conversational intelligence.
*   **Growth Hub:** The section of the "Understand.me" app dedicated to the Growth & Tracking Module, where users access personal insights, badges, resources, etc. (Same as Personal Growth Dashboard).

**H**

*   **Host Path:** The user flow and set of features designed for a Host to create, set up, manage, and facilitate an "Understand.me" session.

**I**

*   **IPFS (InterPlanetary File System):** A decentralized protocol and peer-to-peer network for storing and sharing data in a distributed file system. Used via Nodely for specific data artifacts.

**J**

*   **JWT (JSON Web Token):** A compact, URL-safe means of representing claims to be transferred between two parties. Used by Supabase Auth for managing user sessions.

**N**

*   **NativeWind:** A utility-first styling library that allows using Tailwind CSS-like syntax in React Native applications. An alternative to React Native's StyleSheet API.
*   **Nodely:** A conceptual service in "Understand.me" potentially used for orchestrating complex backend workflows, integrating with IPFS for decentralized storage, or managing specific data pipelines.

**O**

*   **OneTool (AI Orchestration Layer Concept):** If AI Orchestration Layer documentation refers to "OneTool," it likely implies a specific interface, capability, or design pattern within AI Orchestration Layer for handling a particular type of AI orchestration task (e.g., a standardized way AI Orchestration Layer processes a "describe conflict" request).
*   **OTA (Over-The-Air) Updates:** A mechanism (e.g., EAS Update) to deploy updates to the JavaScript bundle and assets of a mobile app directly to users' devices without requiring a new app store submission.

**P**

*   **Participant Path:** The user flow and set of features designed for a Participant joining and engaging in an "Understand.me" session.
*   **Passthrough API (AI Orchestration Layer Concept):** If AI Orchestration Layer documentation refers to a "Passthrough API," it might mean an endpoint on the AI Orchestration Layer that largely forwards requests and responses to an underlying service (like Google GenAI or ElevenLabs) with minimal added logic, possibly for simple tasks or when the client needs more direct control but still benefits from the AI Orchestration Layer's unified interface/authentication.
*   **Personal Growth Insights:** AI-generated feedback and data provided to users about their communication patterns, emotional expression, and progress in the Growth Hub.
*   **AI Orchestration Layer:** The conceptual AI Orchestration layer in "Understand.me." It manages interactions between the Expo app and various AI/backend services (Google GenAI, ElevenLabs, Dappier, Nodely, Supabase, Upstash Redis), handles AI-related state, and contains core logic for AI-mediated features.
*   **PostgreSQL (Postgres):** A powerful, open-source object-relational database system. Supabase uses PostgreSQL as its core database.

**R**

*   **RAG (Retrieval Augmented Generation):** An AI technique where a Large Language Model (LLM) like Google GenAI has its knowledge augmented by retrieving relevant information from an external knowledge base (e.g., via Dappier) before generating a response.
*   **React Native:** A JavaScript framework for building native mobile applications for iOS and Android from a single codebase. Used via Expo in "Understand.me."
*   **React Navigation:** The de facto standard library for routing and navigation in React Native applications.
*   **RLS (Row Level Security):** A feature in PostgreSQL (and thus Supabase) that allows defining fine-grained access control policies on database rows, ensuring users can only access or modify data they are permitted to.

**S**

*   **SDK (Software Development Kit):** A collection of software development tools in one installable package, often providing libraries and utilities to interact with a specific service or platform (e.g., Google GenAI SDK, Supabase JS Client).
*   **Sentry:** An application monitoring platform used for real-time error tracking, performance monitoring, and issue diagnostics.
*   **Serverless:** An architecture model where cloud providers manage the server infrastructure, and applications are run as functions or in managed containers that scale on demand. "Understand.me" employs a serverless-first philosophy.
*   **Session Summary:** An AI-generated document summarizing the key discussions, decisions, and action items from an "Understand.me" session.
*   **STT (Speech-to-Text):** Technology that converts spoken audio into written text. Provided by Google GenAI or potentially ElevenLabs via AI Orchestration Layer.
*   **StyleSheet API (React Native):** The primary way to define styles for React Native components using JavaScript objects.
*   **Supabase:** The Backend as a Service (BaaS) platform used by "Understand.me," providing:
    *   **Supabase Auth:** User authentication and management.
    *   **Supabase Database (PostgreSQL):** For data storage.
    *   **Supabase Edge Functions:** Serverless functions for custom backend logic.
    *   **Supabase Realtime:** For live updates via WebSockets.
    *   **Supabase Storage:** For storing user-uploaded files.

**T**

*   **TPKB (Technical Product Knowledge Base):** This set of documents, providing deep technical reference for "Understand.me."
*   **TTS (Text-to-Speech):** Technology that converts written text into spoken audio. Provided by ElevenLabs for Alex's voice.
*   **TypeScript:** A superset of JavaScript that adds static typing, used for developing the Expo app and potentially backend services for "Understand.me."

**U**

*   **UI Development Guide:** A separate set of documents detailing the UI/UX principles, screen flows, and component designs for the "Understand.me" mobile application.
*   **Upstash Redis:** A serverless Redis provider used as a high-performance caching layer for "Understand.me" to reduce latency and database load, typically accessed by AI Orchestration Layer or Supabase Edge Functions.

**W**

*   **Webhook:** An automated message sent from one application to another when something happens. Used for event-driven communication between services.
