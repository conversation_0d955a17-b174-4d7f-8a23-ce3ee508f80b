# Understand.me - Technical Product Knowledge Base

## Introduction

Welcome to the "Understand.me" Technical Product Knowledge Base (TPKB). This document is designed as a deep technical reference for developers, architects, and product managers involved in the "Understand.me" project. Its primary purpose is to synthesize all critical project information into a cohesive knowledge base, focusing on the intricate details of technical requirements, the serverless architecture, and the complex interactions between all integrated services that power our Expo (React Native) mobile application.

The TPKB aims to provide a single source of truth for understanding not just *how* the system is built, but *why* it's built that way, capturing the nuances of service integrations (Supabase, AI Orchestration Layer, Google GenAI, ElevenLabs, Dappier, Nodely, Upstash Redis, Sentry) and their collective impact on delivering the application's features. It complements the Developer's Guide and the UI Development Guide by providing a more holistic and deeply interconnected view of the product's technical landscape.

## Table of Contents

1.  [Part 1: Product Vision & Technical Implications](part1_product_vision_technical_implications.md)
2.  [Part 2: Comprehensive Feature Breakdown](part2_comprehensive_feature_breakdown.md)
3.  [Part 3: Deep Dive Service Integrations](part3_deep_dive_service_integrations.md)
4.  [Part 4: Data Flow Architecture Details](part4_data_flow_architecture_details.md)
5.  [Part 5: Serverless Design Patterns Applied](part5_serverless_design_patterns_applied.md)
6.  [Part 6: Scalability, Performance & Reliability - Technical](part6_scalability_performance_reliability_technical.md)
7.  [Part 7: Security Architecture - Technical](part7_security_architecture_technical.md)
8.  [Part 8: Decision Log & Rationale](part8_decision_log_rationale.md)
9.  [Part 9: Glossary](part9_glossary.md)

---
*This knowledge base should be used in conjunction with the [Developer's Guide](../developer_guide/README.md) and the [UI Development Guide](../development_guide/README.md).*
