{"name": "understand-me", "version": "1.0.0", "description": "AI-mediated communication platform with Udine voice agent", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:expo\"", "dev:server": "nodemon server/index.js", "dev:expo": "expo start", "build": "npm run build:server && npm run build:expo", "build:server": "tsc && cp -r server/public dist/", "build:expo": "expo build:web", "deploy": "netlify deploy --prod", "test": "jest", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "type-check": "tsc --noEmit"}, "dependencies": {"@elevenlabs/react": "^0.8.0", "@elevenlabs/client": "^0.8.0", "@google/genai": "^1.5.0", "@langchain/core": "^0.3.0", "@langchain/community": "^0.3.0", "@langchain/google-genai": "^0.1.0", "@langchain/langgraph": "^0.2.0", "@langchain/textsplitters": "^0.1.0", "@react-native-async-storage/async-storage": "^1.23.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "expo": "~51.0.0", "expo-av": "~14.0.6", "expo-constants": "~16.0.2", "expo-status-bar": "~1.12.1", "express": "^4.19.2", "hume": "^0.9.0", "langchain": "^0.3.0", "pg": "^8.11.5", "react": "18.2.0", "react-native": "0.74.0", "react-native-web": "~0.19.10", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/express": "^4.17.21", "@types/pg": "^8.11.6", "@types/react": "~18.2.45", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "typescript": "~5.3.3"}, "keywords": ["ai", "communication", "conflict-resolution", "voice-agent", "expo", "react-native", "elevenlabs", "langchain", "hume-ai", "turn-taking"], "author": "Understand.me Team", "license": "MIT", "private": true}