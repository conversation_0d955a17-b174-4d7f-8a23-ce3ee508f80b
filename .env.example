# Understand.me Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/understand_me
DB_HOST=localhost
DB_PORT=5432
DB_NAME=understand_me
DB_USER=username
DB_PASSWORD=password

# AI Services Configuration

# Google GenAI (Primary AI)
GOOGLE_GENAI_API_KEY=your_google_genai_api_key_here

# ElevenLabs (Voice Agent - Udine)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_AGENT_ID=your_udine_agent_id_here
ELEVENLABS_VOICE_ID=your_udine_voice_id_here

# Hume AI (Emotional Intelligence)
HUME_API_KEY=your_hume_ai_api_key_here
HUME_SECRET_KEY=your_hume_secret_key_here

# <PERSON><PERSON>hain Configuration
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langchain_api_key_here
LANGCHAIN_PROJECT=understand-me

# Authentication & Security
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=http://localhost:19006,http://localhost:8081
ALLOWED_ORIGINS=http://localhost:19006,http://localhost:8081,https://your-domain.com

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_TIMEOUT=3600000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Expo/React Native Configuration (Client-side)
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_ELEVENLABS_AGENT_ID=your_udine_agent_id_here
EXPO_PUBLIC_ENVIRONMENT=development

# Netlify Configuration (for deployment)
NETLIFY_SITE_ID=your_netlify_site_id_here
NETLIFY_AUTH_TOKEN=your_netlify_auth_token_here

# Development Tools
DEBUG=understand-me:*
VERBOSE_LOGGING=false

# Feature Flags
ENABLE_VOICE_ANALYSIS=true
ENABLE_EMOTIONAL_INSIGHTS=true
ENABLE_CONFLICT_ANALYSIS=true
ENABLE_PHASE_AUTOMATION=true

# External Integrations
WEBHOOK_SECRET=your_webhook_secret_here
ANALYTICS_KEY=your_analytics_key_here

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30

# Performance Monitoring
PERFORMANCE_MONITORING=true
ERROR_REPORTING=true

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here
FROM_EMAIL=<EMAIL>

# Timezone
TZ=UTC

# API Versioning
API_VERSION=v1

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
